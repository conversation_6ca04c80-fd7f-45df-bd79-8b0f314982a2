# 分布式订单检查系统

基于Spring Boot 2.7 + Vue3 + Nacos的分布式订单检查系统，用于从api_mz_pay_order获取订单信息，通过前端按钮触发检查，并将对比结果存储到另一个数据库。

## 项目结构

```
order-check-system/
├── common/                     # 公共模块
├── order-query-service/        # 订单查询服务 (端口: 8081)
├── order-check-service/        # 订单检查服务 (端口: 8082)
├── order-result-service/       # 结果存储服务 (端口: 8083)
├── order-web-frontend/         # Vue3前端应用 (端口: 8080)
├── docker-compose.yml          # Docker配置文件
├── nacos-mysql.sql            # Nacos数据库初始化脚本
└── order_check_result.sql     # 结果存储数据库脚本
```

## 技术栈

### 后端
- Spring Boot 2.7.18
- Spring Cloud 2021.0.8
- Spring Cloud Alibaba 2021.0.5.0
- Nacos 2.2.3 (服务注册与配置中心)
- MyBatis Plus 3.5.3.1
- MySQL 8.0
- Redis 7.0
- Druid 1.2.18

### 前端
- Vue 3
- Element Plus
- Axios
- Vue Router

## 环境要求

- JDK 8+
- Maven 3.6+
- Node.js 16+
- MySQL 8.0
- Redis 7.0
- Docker (用于Nacos)

## 快速开始

### 1. 启动基础服务

```bash
# 启动Nacos
docker-compose up -d

# 创建结果存储数据库
mysql -h127.0.0.1 -uroot -pHwc132465 < order_check_result.sql
```

### 2. 启动后端服务

```bash
# 编译项目
mvn clean install

# 启动订单查询服务
cd order-query-service
mvn spring-boot:run

# 启动订单检查服务
cd order-check-service
mvn spring-boot:run

# 启动结果存储服务
cd order-result-service
mvn spring-boot:run
```

### 3. 启动前端应用

```bash
cd order-web-frontend
npm install
npm run dev
```

## 数据库配置

### 源数据库 (dytkf)
- 地址: 192.168.1.228:3306
- 用户名: dyt
- 密码: dyt
- 数据库: dytkf
- 表: api_mz_pay_order

### 结果存储数据库
- 地址: 127.0.0.1:3306
- 用户名: root
- 密码: Hwc132465
- 数据库: order_check_result

### Redis配置
- 地址: 127.0.0.1:6379
- 密码: HdkcRedis123!@#

## API接口

### 订单查询服务 (8081)
- GET /api/order/query/page - 分页查询订单
- GET /api/order/query/detail/{orderNo} - 查询订单详情
- GET /api/order/query/pending - 查询待检查订单

### 订单检查服务 (8082)
- POST /api/order/check/single - 单个订单检查
- POST /api/order/check/batch - 批量订单检查

### 结果存储服务 (8083)
- POST /api/order/result/save - 保存检查结果
- GET /api/order/result/page - 分页查询检查结果

## 功能特性

1. **分布式架构**: 基于Nacos的微服务架构
2. **订单查询**: 从api_mz_pay_order表查询订单信息
3. **订单检查**: 调用外部API进行订单验证
4. **结果存储**: 将检查结果存储到独立数据库
5. **前端界面**: Vue3响应式界面，支持订单列表和检查操作
6. **监控管理**: 集成Druid监控和Spring Boot Actuator

## 开发说明

- 订单号(PayTradeNo)来自api_mz_pay_order的order_no字段
- 支付金额(PayAmount)来自api_mz_pay_order的total_price字段
- 检查结果包含匹配状态、金额差异等信息
- 支持单个和批量订单检查
- 提供完整的日志记录和错误处理

## 注意事项

1. 确保Nacos服务正常运行
2. 检查数据库连接配置
3. 确认Redis连接正常
4. 外部API地址需要在配置中正确设置
