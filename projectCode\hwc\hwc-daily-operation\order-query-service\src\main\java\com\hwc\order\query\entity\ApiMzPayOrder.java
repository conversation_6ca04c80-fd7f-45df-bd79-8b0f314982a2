package com.hwc.order.query.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 门诊支付订单表
 *
 * <AUTHOR>
 */
@ApiModel(description = "门诊支付订单")
@TableName("api_mz_pay_order")
public class ApiMzPayOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "医院ID")
    private String hosId;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "患者主键")
    private Integer patientId;

    @ApiModelProperty(value = "患者就诊卡ID")
    private String jzCard;

    @ApiModelProperty(value = "就诊人姓名")
    private String patientName;

    @ApiModelProperty(value = "单据号")
    private String billId;

    @ApiModelProperty(value = "开单时间")
    private String billTime;

    @ApiModelProperty(value = "开单科室")
    private String depName;

    @ApiModelProperty(value = "开单医生")
    private String docName;

    @ApiModelProperty(value = "费用金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "处方类型")
    private String billType;

    @ApiModelProperty(value = "创建时间")
    private Integer createTime;

    @ApiModelProperty(value = "更新时间")
    private Integer updateTime;

    @ApiModelProperty(value = "调用his支付接口返回")
    private String resMsg;

    @ApiModelProperty(value = "订单状态")
    private Integer status;

    @ApiModelProperty(value = "序号")
    private String queueSn;

    @ApiModelProperty(value = "就诊地址")
    private String jzAddress;

    @ApiModelProperty(value = "退款时间")
    private Integer refundTime;

    @ApiModelProperty(value = "his订单号")
    private String hisOrderNo;

    @ApiModelProperty(value = "操作次数")
    private Integer handleTimes;

    @ApiModelProperty(value = "是否打印")
    private Integer hasP;

    @ApiModelProperty(value = "电子发票信息")
    private String eInvoiceInfo;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getHosId() {
        return hosId;
    }

    public void setHosId(String hosId) {
        this.hosId = hosId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getPatientId() {
        return patientId;
    }

    public void setPatientId(Integer patientId) {
        this.patientId = patientId;
    }

    public String getJzCard() {
        return jzCard;
    }

    public void setJzCard(String jzCard) {
        this.jzCard = jzCard;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getBillTime() {
        return billTime;
    }

    public void setBillTime(String billTime) {
        this.billTime = billTime;
    }

    public String getDepName() {
        return depName;
    }

    public void setDepName(String depName) {
        this.depName = depName;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQueueSn() {
        return queueSn;
    }

    public void setQueueSn(String queueSn) {
        this.queueSn = queueSn;
    }

    public String getJzAddress() {
        return jzAddress;
    }

    public void setJzAddress(String jzAddress) {
        this.jzAddress = jzAddress;
    }

    public Integer getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Integer refundTime) {
        this.refundTime = refundTime;
    }

    public String getHisOrderNo() {
        return hisOrderNo;
    }

    public void setHisOrderNo(String hisOrderNo) {
        this.hisOrderNo = hisOrderNo;
    }

    public Integer getHandleTimes() {
        return handleTimes;
    }

    public void setHandleTimes(Integer handleTimes) {
        this.handleTimes = handleTimes;
    }

    public Integer getHasP() {
        return hasP;
    }

    public void setHasP(Integer hasP) {
        this.hasP = hasP;
    }

    public String geteInvoiceInfo() {
        return eInvoiceInfo;
    }

    public void seteInvoiceInfo(String eInvoiceInfo) {
        this.eInvoiceInfo = eInvoiceInfo;
    }
}
