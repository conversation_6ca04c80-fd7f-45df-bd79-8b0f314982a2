@echo off
echo Testing Order Check System...
echo.

echo 1. Checking Nacos status...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8848/nacos' -TimeoutSec 5; Write-Host 'Nacos is running' } catch { Write-Host 'Nacos is not accessible' }"

echo.
echo 2. Checking Docker containers...
docker ps --filter "name=nacos-server" --format "table {{.Names}}\t{{.Status}}"

echo.
echo 3. Testing Maven compilation...
call mvn clean compile -q
if %errorlevel% equ 0 (
    echo Maven compilation successful
) else (
    echo Maven compilation failed
)

echo.
echo 4. Checking frontend dependencies...
cd order-web-frontend
if exist node_modules (
    echo Frontend dependencies already installed
) else (
    echo Frontend dependencies not installed, run: npm install
)
cd ..

echo.
echo 5. System status summary:
echo - Nacos: Check output above
echo - Maven: Check output above  
echo - Frontend: Check output above
echo.
echo To start the system manually, follow MANUAL_START.md

pause
