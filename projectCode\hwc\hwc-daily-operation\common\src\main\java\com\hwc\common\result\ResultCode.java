package com.hwc.common.result;

/**
 * 结果状态码枚举
 *
 * <AUTHOR>
 */
public enum ResultCode {

    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 参数相关
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(401, "参数缺失"),
    PARAM_INVALID(402, "参数无效"),
    
    // 业务相关
    ORDER_NOT_FOUND(1001, "订单不存在"),
    ORDER_CHECK_FAILED(1002, "订单检查失败"),
    ORDER_ALREADY_CHECKED(1003, "订单已检查"),
    
    // 外部接口相关
    EXTERNAL_API_ERROR(2001, "外部接口调用失败"),
    EXTERNAL_API_TIMEOUT(2002, "外部接口调用超时"),
    EXTERNAL_API_UNAVAILABLE(2003, "外部接口不可用"),
    
    // 数据库相关
    DATABASE_ERROR(3001, "数据库操作失败"),
    DATA_NOT_FOUND(3002, "数据不存在"),
    DATA_DUPLICATE(3003, "数据重复"),
    
    // 系统相关
    SYSTEM_ERROR(9001, "系统错误"),
    SYSTEM_BUSY(9002, "系统繁忙"),
    SYSTEM_MAINTENANCE(9003, "系统维护中");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
