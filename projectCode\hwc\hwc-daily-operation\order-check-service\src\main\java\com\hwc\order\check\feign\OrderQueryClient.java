package com.hwc.order.check.feign;

import com.hwc.common.dto.OrderInfoDTO;
import com.hwc.common.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 订单查询服务客户端
 *
 * <AUTHOR>
 */
@FeignClient(name = "order-query-service", path = "/api/order/query")
public interface OrderQueryClient {

    /**
     * 根据订单号查询订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    @GetMapping("/detail/{orderNo}")
    Result<OrderInfoDTO> getOrderDetail(@PathVariable("orderNo") String orderNo);
}
