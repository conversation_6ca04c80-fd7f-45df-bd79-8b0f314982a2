/*
 Navicat Premium Data Transfer

 Source Server         : 滇医通数据库-测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : *************:3306
 Source Schema         : dytkf

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 20/06/2025 14:57:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for DATABASECHANGELOG
-- ----------------------------
DROP TABLE IF EXISTS `DATABASECHANGELOG`;
CREATE TABLE `DATABASECHANGELOG`  (
  `ID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `AUTHOR` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `FILENAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `DATEEXECUTED` datetime NOT NULL,
  `ORDEREXECUTED` int NOT NULL,
  `EXECTYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `MD5SUM` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DESCRIPTION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `COMMENTS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `TAG` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LIQUIBASE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CONTEXTS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LABELS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DEPLOYMENT_ID` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for DATABASECHANGELOGLOCK
-- ----------------------------
DROP TABLE IF EXISTS `DATABASECHANGELOGLOCK`;
CREATE TABLE `DATABASECHANGELOGLOCK`  (
  `ID` int NOT NULL,
  `LOCKED` bit(1) NOT NULL,
  `LOCKGRANTED` datetime NULL DEFAULT NULL,
  `LOCKEDBY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for api_a_temp
-- ----------------------------
DROP TABLE IF EXISTS `api_a_temp`;
CREATE TABLE `api_a_temp`  (
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未处理1已处理',
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 478 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany`;
CREATE TABLE `api_accompany`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `item_name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '陪诊栏目名',
  `item_slogan` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '项目标语',
  `can_treat` tinyint NOT NULL DEFAULT 0 COMMENT '-1不限数',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '陪诊价格',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '-1禁用0开发1启用',
  `price_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '价格说明',
  `item_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务说明',
  `item_notice` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '注意事项',
  `item_flow` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序越大越靠前',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `type` int NULL DEFAULT NULL COMMENT '类型',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `today_time` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当天不能预约时间端',
  `second_time` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第二天不能预约时间端',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '陪诊项目表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_arrange
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_arrange`;
CREATE TABLE `api_accompany_arrange`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院主键',
  `accompany_id` int NOT NULL COMMENT '陪诊项目id',
  `on_week` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '周几',
  `on_time_type` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'am/pm',
  `on_time_str` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '8:00-12:00',
  `max_treat_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '治疗人数',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `is_datepart` tinyint NOT NULL DEFAULT 0 COMMENT '是否分时段排班',
  `datepart` int NOT NULL DEFAULT 0 COMMENT '切割的小时段时间',
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `operator_id` int NULL DEFAULT NULL COMMENT '修改人id',
  `create_operator_id` int NULL DEFAULT NULL COMMENT '创建人id',
  `user_id` int NOT NULL COMMENT '陪诊人id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '陪诊排班规律' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_hospital
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_hospital`;
CREATE TABLE `api_accompany_hospital`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` int NOT NULL COMMENT '医院code',
  `admin_id` int NOT NULL COMMENT '陪诊负责人',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态2正常,1开发中,正常0禁用',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `is_outpatient` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否门诊0否1是',
  `outpatient_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '门诊价格',
  `is_report_send` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否报告邮寄0否1是',
  `report_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '报告价格',
  `is_item` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启单项',
  `user_id` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理员用户id/微信openid',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '预约须知',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '陪诊医院' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_item
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_item`;
CREATE TABLE `api_accompany_item`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简介',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `hos_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院ids',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态0禁用1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置单项' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_item_hospital
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_item_hospital`;
CREATE TABLE `api_accompany_item_hospital`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` int NOT NULL COMMENT '医院code',
  `item_id` int NOT NULL COMMENT '单项id',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `update_time` int NOT NULL COMMENT '修改时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0禁用1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置项目医院' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_payinfo
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_payinfo`;
CREATE TABLE `api_accompany_payinfo`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mch_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户ID',
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户标识',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总价',
  `real_pay_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际付款',
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户订单号',
  `transaction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付订单号',
  `pay_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付时间',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_no_index_pay`(`order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信支付流水表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_phone_bind
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_phone_bind`;
CREATE TABLE `api_accompany_phone_bind`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `out_id` int NOT NULL DEFAULT 0 COMMENT '订单ID',
  `sub_id` int NOT NULL DEFAULT 0 COMMENT '绑定ID',
  `phone_a` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'A号码',
  `phone_b` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'B号码',
  `phone_x` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '中间号',
  `expiration` int NOT NULL DEFAULT 0 COMMENT '过期时间',
  `status` int NOT NULL DEFAULT 1 COMMENT '绑定状态:1绑定,0已解绑',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '陪诊号码绑定' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_record
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_record`;
CREATE TABLE `api_accompany_record`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户主键',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '就诊人Id',
  `patient_name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人姓名',
  `patient_id_card` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人身份证',
  `patient_phone` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人电话',
  `accompany_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '预约类型',
  `accompany_name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '陪诊名',
  `item_id` int NULL DEFAULT NULL COMMENT '单项id',
  `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单项名',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `is_register` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否需要挂号0不需要1需要待挂号2已协助',
  `order_status` tinyint NOT NULL DEFAULT 1 COMMENT '订单状态-1已撤销0已申请退款1正常2已退款',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '0待支付1已支付-1已退款',
  `order_no` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付编号',
  `hospital_code` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `hospital_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预约医院名',
  `sch_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '预约时间',
  `sch_date` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊日期',
  `sch_date_time` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时候上午/下午',
  `schedule_id` int NULL DEFAULT NULL COMMENT '排班id',
  `accompany_user` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '陪诊人',
  `accompany_user_phone` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '陪诊人手机',
  `accompany_user_id` int NOT NULL DEFAULT 0 COMMENT '陪诊人user_id',
  `accompany_status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '陪诊状态0未陪诊1陪诊中2已陪诊',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `cancel_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '取消时间',
  `notice` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '异常信息',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '病情描述',
  `postal_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮递地址',
  `appoint_id` int NOT NULL DEFAULT 0 COMMENT '挂号id',
  `pay_cancal_time` int NULL DEFAULT NULL COMMENT '未支付取消时间',
  `other_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '监护人电话',
  `remake` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 398 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '陪诊订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_refund
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_refund`;
CREATE TABLE `api_accompany_refund`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `record_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '陪诊记录主键',
  `order_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `patient_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人姓名',
  `hospital_code` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `hospital_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院名',
  `accompany_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '陪诊项目',
  `apply_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请时间',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0为退款1已退款',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `admin_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款人主键',
  `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单项名',
  `accompany_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '陪诊人',
  `patient_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊人电话',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_accompany_work
-- ----------------------------
DROP TABLE IF EXISTS `api_accompany_work`;
CREATE TABLE `api_accompany_work`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院表主键',
  `company_id` int NOT NULL COMMENT '陪诊项目id',
  `max_treat` smallint UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大治疗人数',
  `used_treat` smallint UNSIGNED NOT NULL DEFAULT 0 COMMENT '已经预约数量',
  `work_time` int NOT NULL DEFAULT 0 COMMENT '工作日期',
  `work_time_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'am/pm',
  `work_time_str` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '时间段',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态1开诊0停诊',
  `sort` int UNSIGNED NOT NULL DEFAULT 255,
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `operator_id` int NULL DEFAULT NULL COMMENT '修改人id',
  `create_operator_id` int NULL DEFAULT NULL COMMENT '创建人id',
  `user_id` int NOT NULL COMMENT '陪诊人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_doctor_work_work_time_IDX`(`work_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1146 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '陪诊排班' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_active
-- ----------------------------
DROP TABLE IF EXISTS `api_active`;
CREATE TABLE `api_active`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `openid` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1542175 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_activity_enroll
-- ----------------------------
DROP TABLE IF EXISTS `api_activity_enroll`;
CREATE TABLE `api_activity_enroll`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `hos_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院code',
  `user_id` int NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户电话',
  `user_sex` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '男' COMMENT '用户性别',
  `user_age` int NULL DEFAULT NULL COMMENT '用户年龄',
  `user_height` float(4, 1) NULL DEFAULT NULL COMMENT '用户年龄',
  `user_weight` float(4, 1) NULL DEFAULT NULL COMMENT '用户体重',
  `activity_id` int NOT NULL COMMENT '活动ID',
  `activity_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动标题',
  `activity_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '活动价格',
  `activity_date` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `activity_content` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动（选择）内容',
  `attendance` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出席人数',
  `check_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销码',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:1=预约成功,-1=已撤销,2-已完成',
  `pay_status` int NULL DEFAULT 0 COMMENT '支付状态 :-2=已退款,-1=不需要支付,0=待支付,1=已支付',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院活动报名表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_adclick_count
-- ----------------------------
DROP TABLE IF EXISTS `api_adclick_count`;
CREATE TABLE `api_adclick_count`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `openid` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户标识',
  `ad_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '广告主健',
  `click_date` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '创建时间',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1957 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_admin_openid
-- ----------------------------
DROP TABLE IF EXISTS `api_admin_openid`;
CREATE TABLE `api_admin_openid`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员表主键',
  `openid` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_advertisement
-- ----------------------------
DROP TABLE IF EXISTS `api_advertisement`;
CREATE TABLE `api_advertisement`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '广告标题',
  `type` tinyint(1) NOT NULL COMMENT '广告类型',
  `ad_picture` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '广告图片',
  `ad_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '广告链接地址',
  `key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '广告说明',
  `is_show` tinyint UNSIGNED NOT NULL COMMENT '广告显示状态',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `views` int NOT NULL DEFAULT 0 COMMENT '点击浏览次数',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `position` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '广告位位置标识1滇医通首页2详情页3问诊首页',
  `match_word` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关键词',
  `start_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `end_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 320 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '广告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_follow
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_follow`;
CREATE TABLE `api_adviser_follow`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `adviser_id` int NOT NULL DEFAULT 0 COMMENT '顾问ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '用户ID',
  `follow_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '关注类型1：关注2：取消关注',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 87433 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊顾问关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_income
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_income`;
CREATE TABLE `api_adviser_income`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `service_id` int NOT NULL DEFAULT 0 COMMENT '服务ID',
  `service_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务名称',
  `adviser_id` int NOT NULL DEFAULT 0 COMMENT '就诊顾问ID',
  `adviser_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊顾问ID',
  `price` decimal(6, 2) NOT NULL DEFAULT 0.00 COMMENT '收入费用',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态(  0未提现   1已提现)',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76612 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊顾问收入表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_order
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_order`;
CREATE TABLE `api_adviser_order`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT ' 订单号',
  `adviser_id` int NOT NULL DEFAULT 0 COMMENT '顾问ID',
  `adviser_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '顾问名字',
  `verify_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '验证码',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '患者ID',
  `user_nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户微信昵称',
  `user_openid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'openid',
  `patient_id` int NULL DEFAULT 0 COMMENT '就诊人ID',
  `patient_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '就诊人姓名',
  `patient_idcard` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '身份证',
  `patient_phone` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '电话',
  `service_id` int NOT NULL DEFAULT 0 COMMENT '顾问服务ID',
  `service_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '顾问服务名称',
  `service_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '服务时间',
  `total_price` decimal(6, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态（0：待支付，1：已支付，2：申请退款，3：退款成功，4：拒绝退款）',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（-1：已取消，0：未完成，1：已完成）',
  `user_address_id` int NULL DEFAULT 0 COMMENT '用户地址ID',
  `user_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户地址信息',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注信息',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间（支付时间） ',
  `refuse_msg` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '拒绝原因',
  `refund_time` int NULL DEFAULT 0 COMMENT '退款时间',
  `cancel_time` int NULL DEFAULT 0 COMMENT '更新时间（取消退款时间）',
  `cancel_msg` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '退款原因',
  `end_time` int NULL DEFAULT 0 COMMENT '完成时间',
  `adviser_assess_time` int NULL DEFAULT 0 COMMENT '顾问退款审核时间',
  `admin_assess_id` int NULL DEFAULT 0 COMMENT '管理员退款审核者',
  `admin_assess_time` int NULL DEFAULT 0 COMMENT '管理员退款审核时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100380 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊顾问服务订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_refund
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_refund`;
CREATE TABLE `api_adviser_refund`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '用户ID',
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户openid',
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '退款原因',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '退款申请时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态(0未退款1已退款)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_user
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_user`;
CREATE TABLE `api_adviser_user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机号',
  `password` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `token` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '验证令牌',
  `name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `wechat_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `wechat_qrcode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '微信二维码',
  `qrcode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '推广二维码',
  `openid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'openid',
  `cid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'app cid',
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `summary` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '简介',
  `hospital_type` int NULL DEFAULT 1 COMMENT '医院类型',
  `hospital_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `hospital_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `experience` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '经验年限',
  `service_sum` int NOT NULL DEFAULT 0 COMMENT '服务人次',
  `follow_sum` int NOT NULL DEFAULT 0 COMMENT '关注数',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态（0：禁用，1：启用, 2 : 忙碌）',
  `lastlogin_time` int NULL DEFAULT 0 COMMENT '最后登录时间',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  `user_agent` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'user_agent',
  `phone_type` int NULL DEFAULT 0 COMMENT '手机类型0未知 1安卓 2苹果 ',
  `service_start_time` time NULL DEFAULT NULL COMMENT '服务开始时间',
  `service_end_time` time NULL DEFAULT NULL COMMENT '服务结束时间',
  `role_type` int NULL DEFAULT 1 COMMENT '角色：1：就诊顾问 2：直通车顾问 3：就诊顾问且直通车顾问',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊顾问表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_adviser_user_service
-- ----------------------------
DROP TABLE IF EXISTS `api_adviser_user_service`;
CREATE TABLE `api_adviser_user_service`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `adviser_id` int NOT NULL DEFAULT 0 COMMENT '顾问ID',
  `service_id` int NOT NULL DEFAULT 0 COMMENT '服务ID',
  `cps` int NOT NULL DEFAULT 0 COMMENT '分成百分比',
  `need_patient` tinyint(1) NULL DEFAULT 0 COMMENT '是否需要指定就诊人',
  `need_date` tinyint(1) NULL DEFAULT 0 COMMENT '是否指定日期',
  `need_address` tinyint(1) NULL DEFAULT 0 COMMENT '是否需要地址',
  `service_init_num` int NULL DEFAULT 0 COMMENT '初始服务人数',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（0：禁用，1：启用）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊顾问服务关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_age
-- ----------------------------
DROP TABLE IF EXISTS `api_age`;
CREATE TABLE `api_age`  (
  `id` tinyint UNSIGNED NOT NULL AUTO_INCREMENT,
  `num` int UNSIGNED NOT NULL DEFAULT 0,
  `x` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '0',
  `y` int UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 132 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_agreement
-- ----------------------------
DROP TABLE IF EXISTS `api_agreement`;
CREATE TABLE `api_agreement`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '协议名称',
  `business_id` int NOT NULL COMMENT '业务',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型1用户端2医生端',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0禁用1启用',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `deleted` int NOT NULL DEFAULT 0 COMMENT '删除0否1是',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `optional` int NOT NULL DEFAULT 0 COMMENT '可选',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议列表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_agreement_business
-- ----------------------------
DROP TABLE IF EXISTS `api_agreement_business`;
CREATE TABLE `api_agreement_business`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `business_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务名',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态1启用0禁用',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `type` int NULL DEFAULT NULL COMMENT '类型',
  `deleted` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议业务' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_agreement_user
-- ----------------------------
DROP TABLE IF EXISTS `api_agreement_user`;
CREATE TABLE `api_agreement_user`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL DEFAULT 1 COMMENT '类型1用户端2医生端',
  `agree_id` int NOT NULL COMMENT '协议id',
  `agree_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '协议名称',
  `version_id` int NOT NULL COMMENT '协议版本id',
  `version_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '协议版本名称',
  `user_id` int NOT NULL COMMENT '用户id,医生id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `business_id` int NULL DEFAULT NULL COMMENT '业务id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_userid`(`user_id` ASC) USING BTREE COMMENT 'create by DAS-3c63eb66-3917-425a-9229-eff8af050e98'
) ENGINE = InnoDB AUTO_INCREMENT = 186150 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_agreement_version
-- ----------------------------
DROP TABLE IF EXISTS `api_agreement_version`;
CREATE TABLE `api_agreement_version`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `agree_id` int NOT NULL COMMENT '协议id',
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本型号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态-1暂存1启用0禁用',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `admin_id` int NULL DEFAULT NULL COMMENT '发布人id',
  `release_time` int NOT NULL COMMENT '发布时间',
  `deleted` int NOT NULL DEFAULT 0 COMMENT '删除0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议版本' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_alldoctor_bak
-- ----------------------------
DROP TABLE IF EXISTS `api_alldoctor_bak`;
CREATE TABLE `api_alldoctor_bak`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生ID',
  `doc_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生编码',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_tag` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生标签',
  `status` tinyint UNSIGNED NOT NULL COMMENT '医生状态',
  `doc_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_alldoctors
-- ----------------------------
DROP TABLE IF EXISTS `api_alldoctors`;
CREATE TABLE `api_alldoctors`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生ID',
  `doc_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生编码',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_mysql500_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_tag` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生标签',
  `status` tinyint UNSIGNED NOT NULL COMMENT '医生状态',
  `doc_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6058 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_antigen_reagent
-- ----------------------------
DROP TABLE IF EXISTS `api_antigen_reagent`;
CREATE TABLE `api_antigen_reagent`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `hos_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院code',
  `user_id` int NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户电话',
  `user_sex` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '男' COMMENT '用户性别',
  `user_age` int NULL DEFAULT NULL COMMENT '用户年龄',
  `ID_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户地址',
  `user_height` float(4, 1) NULL DEFAULT NULL COMMENT '用户年龄',
  `user_weight` float(4, 1) NULL DEFAULT NULL COMMENT '用户体重',
  `activity_id` int NULL DEFAULT NULL COMMENT '活动ID',
  `activity_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动标题',
  `activity_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '活动价格',
  `activity_date` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `activity_content` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动（选择）内容',
  `attendance` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出席人数',
  `check_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销码',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:1=预约成功,-1=已撤销,2-已完成',
  `pay_status` int NULL DEFAULT 0 COMMENT '支付状态 :-2=已退款,-1=不需要支付,0=待支付,1=已支付',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抗原试剂报名表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_app_publish
-- ----------------------------
DROP TABLE IF EXISTS `api_app_publish`;
CREATE TABLE `api_app_publish`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `version_id` int NOT NULL COMMENT '版本id',
  `version_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本号',
  `version_num` int NOT NULL COMMENT '版本数字',
  `update_policy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '更新策略 0静默更新1每次提醒2单次提醒3强制更新 ',
  `status` int NOT NULL COMMENT '状态0下线1上线',
  `client_type` int NOT NULL DEFAULT 1 COMMENT '系统版本1安卓2苹果',
  `is_grayscale` tinyint(1) NOT NULL COMMENT '是否灰度0否1是',
  `scale` int NOT NULL DEFAULT 0 COMMENT '灰度比例',
  `is_grayscale_number` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否取余数0否1是',
  `is_grayscale_user` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否指定人员0否1是',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `publisher_id` int NOT NULL COMMENT '发布id',
  `publisher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下载地址',
  `old_id` int NULL DEFAULT 0 COMMENT '正式上个版本',
  `is_formal` int NOT NULL DEFAULT 0 COMMENT '是否正式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'app发布' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_app_publish_user
-- ----------------------------
DROP TABLE IF EXISTS `api_app_publish_user`;
CREATE TABLE `api_app_publish_user`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NULL DEFAULT NULL COMMENT '用户id',
  `publish_id` int NOT NULL COMMENT '发布id',
  `doctor_id` int NULL DEFAULT NULL COMMENT '医生团队id',
  `doctor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生名',
  `hospital_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 69 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'app灰度指定人员' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_app_version
-- ----------------------------
DROP TABLE IF EXISTS `api_app_version`;
CREATE TABLE `api_app_version`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本',
  `console_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '更新内容',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下载地址',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `client_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型 1安卓 2苹果',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'app版本管理' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_applet_article
-- ----------------------------
DROP TABLE IF EXISTS `api_applet_article`;
CREATE TABLE `api_applet_article`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int NOT NULL DEFAULT 0 COMMENT '上级id',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频链接',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `show_time` int NULL DEFAULT NULL COMMENT '显示时间',
  `inputer_user` int NULL DEFAULT NULL COMMENT '推送订阅人',
  `is_push` int NOT NULL DEFAULT 0 COMMENT '是否推送0否1是',
  `push_time` int NULL DEFAULT NULL COMMENT '推送时间',
  `title_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送主标题',
  `title_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送副标题',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 246 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序文章' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_appoint_dep_mapping
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_dep_mapping`;
CREATE TABLE `api_appoint_dep_mapping`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `appoint_record_id` int NOT NULL COMMENT '预约记录ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `extra_dep_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附加科室ID',
  `extra_dep_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附加科室名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 421390 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_appoint_diagnosis_report
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_diagnosis_report`;
CREATE TABLE `api_appoint_diagnosis_report`  (
  `id` int NOT NULL COMMENT '与api_appoint_record中id相同',
  `report_content` blob NULL COMMENT '预问诊报告',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_appoint_record
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_record`;
CREATE TABLE `api_appoint_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_payed_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '延安医院支付完返回的流水号',
  `ext_order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院未支付时的流水号',
  `order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int NOT NULL DEFAULT 0 COMMENT '就诊人ID',
  `jz_card` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '就诊卡号',
  `patient_id_card` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '就诊人身份证号',
  `patient_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人手机号',
  `hos_id` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `dep_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生姓名',
  `schedule_id` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '排班ID 延安是汉字',
  `queue_sn` int NULL DEFAULT 0 COMMENT '预约序号',
  `yyid` int NOT NULL DEFAULT 0 COMMENT '预约id,流水号',
  `hyid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '号源ID',
  `hzid` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '患者id/门诊id,当患者要删记录，把user_id保存过来',
  `start_time` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分时段预约开始时间',
  `end_time` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分时段预约结束时间',
  `sch_date` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊日期',
  `time_type` int NOT NULL COMMENT '1:上午,2:下午,3:中午,4:晚上',
  `jz_start_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊开始时间',
  `jz_address` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊地址',
  `amt` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `pay_status` int NULL DEFAULT NULL COMMENT '支付状态 (-2:已退款,-1:不需要支付,0:待支付,1:已支付)',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `cancel_time` int NOT NULL DEFAULT 0 COMMENT '撤销预约的时间',
  `status` int NOT NULL DEFAULT 1 COMMENT '预约状态:(1:预约成功,-1:已撤销,-2已删除)',
  `ghf` decimal(10, 2) NULL DEFAULT NULL COMMENT '挂号费',
  `zjf` decimal(10, 2) NULL DEFAULT NULL COMMENT '专家费',
  `zlf` decimal(10, 2) NULL DEFAULT NULL COMMENT '诊疗费',
  `cancel_times` int NOT NULL DEFAULT 0 COMMENT '撤销次数',
  `his_status` int NOT NULL DEFAULT 0 COMMENT '1:微信支付成功,his支付失败;2:his退款成功,微信退款失败 小系统是就诊状态0未就诊6已就诊',
  `stop_msg_times` int NOT NULL DEFAULT 0 COMMENT '停诊短信通知次数',
  `sign` int NOT NULL DEFAULT 0 COMMENT '就诊签到标识',
  `appoint_channel` int NULL DEFAULT 0 COMMENT '预约渠道标识,2:微信城市服务,1:公众号',
  `cancel_channel` int NOT NULL DEFAULT 0 COMMENT '取消渠道',
  `ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '渠道',
  `consultation_status` tinyint NOT NULL DEFAULT 0 COMMENT '会诊 0 待审核 1过审 2 不通过',
  `refund_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款人',
  `appoint_qrcode` tinytext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '预约二维码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_no_index`(`order_no` ASC) USING BTREE,
  INDEX `patient_id_index`(`patient_id` ASC, `hos_id` ASC) USING BTREE,
  INDEX `patient_phone_index`(`patient_phone` ASC) USING BTREE,
  INDEX `patient_name_index`(`patient_name` ASC) USING BTREE,
  INDEX `idx_cancel_time_hos`(`cancel_time` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_sch_date_hos_status`(`sch_date` ASC, `hos_id` ASC, `status` ASC) USING BTREE,
  INDEX `idx_user_hos`(`user_id` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_doc_hos_createtime_dep`(`doc_name` ASC, `hos_id` ASC, `create_time` ASC, `dep_id` ASC) USING BTREE,
  INDEX `idx_channel_createtime_hos`(`channel` ASC, `create_time` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_hos_dep_doc_schedule`(`hos_id` ASC, `dep_id` ASC, `doc_id` ASC, `schedule_id` ASC) USING BTREE,
  INDEX `idx_hos_schedule`(`hos_id` ASC, `schedule_id` ASC) USING BTREE,
  INDEX `idx_hosid_createtime_dep`(`hos_id` ASC, `create_time` ASC, `dep_id` ASC) USING BTREE,
  INDEX `idx_patient_id_card_createtime`(`patient_id_card` ASC, `create_time` ASC) USING BTREE,
  INDEX `idx_updatetime`(`update_time` ASC) USING BTREE,
  INDEX `idx_createtime_orderno`(`create_time` ASC, `order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27177183 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预约记录' ROW_FORMAT = COMPRESSED;

-- ----------------------------
-- Table structure for api_appoint_record_ali
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_record_ali`;
CREATE TABLE `api_appoint_record_ali`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `appoint_record_id` int NOT NULL COMMENT '预约id',
  `ali_union_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '阿里健康id或支付宝id',
  `ali_app_channel` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '阿里健康预约渠道',
  `is_test` tinyint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3500 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_appoint_record_msg
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_record_msg`;
CREATE TABLE `api_appoint_record_msg`  (
  `id` int UNSIGNED NOT NULL COMMENT '订单号',
  `mark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `cancel_msg` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '取消原因',
  `msg_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_appoint_record_no_order_no
-- ----------------------------
DROP TABLE IF EXISTS `api_appoint_record_no_order_no`;
CREATE TABLE `api_appoint_record_no_order_no`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_payed_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '延安医院支付完返回的流水号',
  `ext_order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院未支付时的流水号',
  `order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int NOT NULL DEFAULT 0 COMMENT '就诊人ID',
  `jz_card` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '就诊卡号',
  `patient_id_card` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '就诊人身份证号',
  `patient_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人手机号',
  `hos_id` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `dep_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `schedule_id` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '排班ID 延安是汉字',
  `queue_sn` int NULL DEFAULT 0 COMMENT '预约序号',
  `yyid` int NOT NULL DEFAULT 0 COMMENT '预约id,流水号',
  `hzid` int NOT NULL DEFAULT 0 COMMENT '114患者id',
  `start_time` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分时段预约开始时间',
  `end_time` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分时段预约结束时间',
  `sch_date` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊日期',
  `time_type` int NOT NULL COMMENT '1:上午,2:下午,3:中午,4:晚上',
  `jz_start_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊开始时间',
  `amt` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `msg_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '短信',
  `pay_status` int NULL DEFAULT 0 COMMENT '支付状态(-2:已退款,-1:不需要支付,0:待支付,1:已支付)',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `cancel_time` int NOT NULL DEFAULT 0 COMMENT '撤销预约的时间',
  `status` int NOT NULL DEFAULT 1 COMMENT '预约状态:(1:预约成功,-1:已撤销,-2已删除)',
  `ghf` decimal(10, 2) NULL DEFAULT NULL COMMENT '挂号费',
  `zjf` decimal(10, 2) NULL DEFAULT NULL COMMENT '专家费',
  `zlf` decimal(10, 2) NULL DEFAULT NULL COMMENT '诊疗费',
  `cancel_times` int NOT NULL DEFAULT 0 COMMENT '撤销次数',
  `his_status` int NOT NULL DEFAULT 0 COMMENT '1:微信支付成功,his支付失败;2:his退款成功,微信退款失败',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `stop_msg_times` int NOT NULL DEFAULT 0 COMMENT '停诊短信通知次数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_index`(`user_id` ASC) USING BTREE,
  INDEX `hos_id_index`(`hos_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 340313 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预约记录' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_package_address
-- ----------------------------
DROP TABLE IF EXISTS `api_check_package_address`;
CREATE TABLE `api_check_package_address`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `package_id` int NOT NULL COMMENT '套餐ID',
  `address_id` int NOT NULL COMMENT '地址ID',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 1-启用 0-禁用',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '套餐地址' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_package_item
-- ----------------------------
DROP TABLE IF EXISTS `api_check_package_item`;
CREATE TABLE `api_check_package_item`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '体检机构id',
  `package_id` int NOT NULL COMMENT '体检套餐id',
  `item_id` int NOT NULL COMMENT '加项id',
  `type_id` int NOT NULL COMMENT '加项分类id',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐加项关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_check_package_label
-- ----------------------------
DROP TABLE IF EXISTS `api_check_package_label`;
CREATE TABLE `api_check_package_label`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名',
  `status` int NOT NULL COMMENT '状态-1禁用0开发1启用',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `pid` int NOT NULL DEFAULT 0 COMMENT '上级id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐标签' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_package_project
-- ----------------------------
DROP TABLE IF EXISTS `api_check_package_project`;
CREATE TABLE `api_check_package_project`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '机构ID',
  `package_id` int NOT NULL COMMENT '套餐id',
  `company_project_id` int NOT NULL COMMENT '机构项目ID',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `check_meaning` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '检查意义',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `classify_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类ID，多个用|隔开',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态1-启用 0-禁用',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3459 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '体检套餐项目' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_recommend
-- ----------------------------
DROP TABLE IF EXISTS `api_check_recommend`;
CREATE TABLE `api_check_recommend`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图标',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url连接地址',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体检推广区' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_region
-- ----------------------------
DROP TABLE IF EXISTS `api_check_region`;
CREATE TABLE `api_check_region`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `region_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '区域名称',
  `createtime` int NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  `yn_region_id` int NOT NULL DEFAULT 0 COMMENT '区县',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `weigh`(`weigh` ASC, `id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '区域城市表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_schedule
-- ----------------------------
DROP TABLE IF EXISTS `api_check_schedule`;
CREATE TABLE `api_check_schedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '机构ID',
  `package_id` int NOT NULL COMMENT '机构id',
  `appoint_date` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '可预约日期',
  `max_num` int NOT NULL DEFAULT 0 COMMENT '最大预约数',
  `appoint_num` int NOT NULL DEFAULT 0 COMMENT '已预约人数',
  `is_time` int NOT NULL DEFAULT 0 COMMENT '是否分时段',
  `week` int NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0:停捡,1:开检)',
  `create_time` int NOT NULL DEFAULT 0,
  `update_time` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_packageid_status_appointdate`(`package_id` ASC, `status` ASC, `appoint_date` ASC) USING BTREE COMMENT 'create by DAS-4a318db6-23e1-4c8b-986d-ca6661f64489-0'
) ENGINE = InnoDB AUTO_INCREMENT = 648149 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '体检排班表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_schedule_time
-- ----------------------------
DROP TABLE IF EXISTS `api_check_schedule_time`;
CREATE TABLE `api_check_schedule_time`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `schedule_id` int NOT NULL COMMENT '排班ID',
  `start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开始时间',
  `end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间',
  `time_num` int NOT NULL DEFAULT 0 COMMENT '分时号源数',
  `appoint_num` int NOT NULL DEFAULT 0 COMMENT '分时号源预约数',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态1:可约,0不可约',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `schedule_id`(`schedule_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 206782 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体检分时号源排班表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_check_tag`;
CREATE TABLE `api_check_tag`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `level` int NOT NULL,
  `tag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键词',
  `status` int NULL DEFAULT NULL COMMENT '状态',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体检评价候选关键词' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_team_appointment
-- ----------------------------
DROP TABLE IF EXISTS `api_check_team_appointment`;
CREATE TABLE `api_check_team_appointment`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户id',
  `team_leader_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '团检负责人姓名',
  `team_leader_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '团检负责人联系方式',
  `patient_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '下单人姓名',
  `patient_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '下单人电话',
  `company_id` int NOT NULL COMMENT '体检机构id',
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总价格',
  `total_people` int NOT NULL COMMENT '总人数',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态（0，待支付、1，已支付、2，已退款）',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0，初始状态、1，预约成功、2，预约失败、3，已完成,、4已取消）',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约渠道',
  `is_mail` tinyint(1) NOT NULL COMMENT '是否邮寄',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `write_off_status` bit(1) NULL DEFAULT b'0' COMMENT '核销状态',
  `receipt_status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '发票状态',
  `remarke` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队体检预约主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_check_team_appointment_schedule
-- ----------------------------
DROP TABLE IF EXISTS `api_check_team_appointment_schedule`;
CREATE TABLE `api_check_team_appointment_schedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `appointment_id` int NOT NULL COMMENT '团队体检预约主表ID（外键）',
  `schedule_id` int NOT NULL COMMENT '排班ID',
  `appoint_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预约日期',
  `time_schedule_id` int NOT NULL COMMENT '分时排班id',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_appointment_id`(`appointment_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预约排班关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_check_team_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `api_check_team_package_detail`;
CREATE TABLE `api_check_team_package_detail`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `appointment_id` int NOT NULL COMMENT '预约表ID（外键）',
  `package_id` int NOT NULL COMMENT '套餐id',
  `package_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `package_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐价格',
  `package_people` int NOT NULL COMMENT '套餐对应人数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_appointment_id`(`appointment_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队体检套餐详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_check_team_patient_info
-- ----------------------------
DROP TABLE IF EXISTS `api_check_team_patient_info`;
CREATE TABLE `api_check_team_patient_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `appointment_id` int NOT NULL COMMENT '预约表ID（外键）',
  `patient_id` int NOT NULL COMMENT '就诊人id',
  `patient_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊人电话',
  `patient_gender` enum('M','F') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别（M:男，F:女）',
  `id_number` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '就诊人状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_appointment_id`(`appointment_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '就诊人信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_check_type
-- ----------------------------
DROP TABLE IF EXISTS `api_check_type`;
CREATE TABLE `api_check_type`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父级分类id',
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类图标',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序号',
  `company_id` int NOT NULL DEFAULT 0 COMMENT '分类指定所属机构',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0：禁用，1：启用）',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  `type` int NOT NULL DEFAULT 0 COMMENT '前端分类 0正常 1单独',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_work
-- ----------------------------
DROP TABLE IF EXISTS `api_check_work`;
CREATE TABLE `api_check_work`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '机构ID',
  `package_id` int NOT NULL COMMENT '套餐id',
  `week` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '周几',
  `max_num` int NOT NULL COMMENT '预约最大限额',
  `is_time` int NOT NULL DEFAULT 0 COMMENT '是否分时段',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1479 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_check_work_time
-- ----------------------------
DROP TABLE IF EXISTS `api_check_work_time`;
CREATE TABLE `api_check_work_time`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `work_id` int NOT NULL,
  `week` int NOT NULL COMMENT '星期1,2,3,4,5,6,7',
  `start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开始时间',
  `end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间',
  `time_num` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `work_id`(`work_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9135 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '体检分时排班规律' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_city
-- ----------------------------
DROP TABLE IF EXISTS `api_city`;
CREATE TABLE `api_city`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `city_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sort` int NOT NULL DEFAULT 255,
  `status` tinyint NOT NULL DEFAULT 1,
  `update_time` int NOT NULL,
  `create_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic`;
CREATE TABLE `api_clinic`  (
  `clinic_id` int NOT NULL AUTO_INCREMENT,
  `clinic_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '诊所名称',
  `clinic_detail_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '诊所详情',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `clinic_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `clinic_tel` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_clinic` int NOT NULL DEFAULT 1 COMMENT '0医院1诊所2协会',
  `clinic_stop_appoint_tomorrow` tinyint NOT NULL DEFAULT 0 COMMENT '停止预约明天的时间点',
  `appoint_days` int NOT NULL DEFAULT 1,
  `treat_fee` decimal(10, 2) NOT NULL,
  `treat_fee_unit` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '次' COMMENT '单位',
  `cancel_order_time` int NOT NULL DEFAULT 7,
  `baidu_map_keyword` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `clinic_visit_location` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预约后就诊的详细位置',
  `clinic_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '诊所状态',
  `clinic_logo` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '/uploads/clinic_cover/20170119/cddf423a09309376706d7f348ec3f106.jpg' COMMENT '封面图',
  `clinic_say` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '宣传语',
  `allow_day_back` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '允不允许当天退款，1允许0允许',
  `back_money_time` tinyint UNSIGNED NOT NULL DEFAULT 16 COMMENT '退款时间点',
  `allow_cur_day_appoint` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '允不允许挂当天号1允许0不允许',
  `appoint_url_type` int NOT NULL,
  `appoint_url_param` varchar(225) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '特需预约点击预约按钮跳转参数',
  `clinic_cover` varchar(225) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'cover',
  `notice` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '预约注意事项',
  `icon1` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'icon1图标1',
  `icon2` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'icon2',
  `icon3` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'icon3图标3',
  `icon1_text` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标一说明',
  `icon2_text` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标2说明',
  `icon3_text` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标3 说明',
  `treat_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '该诊所已经看诊总数',
  `clinic_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '诊所介绍',
  `setOrdernum` tinyint NULL DEFAULT 0 COMMENT '是否设置就诊序号',
  `is_category` tinyint NULL DEFAULT 0 COMMENT '是否设置普通，特约入口',
  `list_sort` int NOT NULL DEFAULT 0 COMMENT '列表排序',
  `hos_type` tinyint NOT NULL DEFAULT 3 COMMENT '医院类型',
  `is_show_dep` int NULL DEFAULT 0 COMMENT '是否显示科室,0:不显示,1:显示',
  `is_guidan` int NULL DEFAULT 0 COMMENT '是否开通导诊,0:未开通,1:已开通',
  `clinic_longlat` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '坐标',
  `index_hide` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否首页预约挂号隐藏0否1是',
  PRIMARY KEY (`clinic_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10078 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '诊所表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_appoint
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_appoint`;
CREATE TABLE `api_clinic_appoint`  (
  `appoint_order_id` int NOT NULL AUTO_INCREMENT,
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户openid',
  `clinic_id` int NOT NULL DEFAULT 10001 COMMENT '诊所id',
  `schedule_id` int NULL DEFAULT NULL COMMENT '排班id',
  `appoint_username` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预约用户姓名',
  `appoint_mobile` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预约用户手机号',
  `appoint_order_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预约订单号',
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支付1已支付0未支付',
  `check_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0000' COMMENT '效验码',
  `create_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建时间',
  `update_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '更新时间',
  `appoint_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态0未就诊1已就诊-2已申请退款-1取消',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
  `patient_id` int NOT NULL DEFAULT 0 COMMENT '就诊人id',
  `record_type` int NOT NULL DEFAULT 1 COMMENT '1特需 2中医 3手术',
  `sequence_num` int NULL DEFAULT NULL COMMENT '就诊序号',
  `operation_id` int NOT NULL DEFAULT 0 COMMENT '手术id',
  `id` int NOT NULL,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '渠道',
  PRIMARY KEY (`appoint_order_id`) USING BTREE,
  INDEX `appoint_order_num`(`appoint_order_num` ASC) USING BTREE,
  INDEX `i_user_id`(`user_id` ASC) USING BTREE,
  INDEX `i_schedule_id`(`schedule_id` ASC) USING BTREE,
  INDEX `i_create_time`(`create_time` ASC) USING BTREE,
  INDEX `i_patient_id`(`patient_id` ASC) USING BTREE,
  INDEX `api_clinic_appoint_clinic_id_IDX`(`clinic_id` ASC) USING BTREE,
  INDEX `idx_updatetime`(`update_time` ASC) USING BTREE,
  INDEX `idx_openid_recordtype`(`openid` ASC, `record_type` ASC) USING BTREE COMMENT 'create by DAS-e6721676-c6b6-4a1b-8434-40b30ae1e58e'
) ENGINE = InnoDB AUTO_INCREMENT = 380401 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预约订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_appoint_diagnose
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_appoint_diagnose`;
CREATE TABLE `api_clinic_appoint_diagnose`  (
  `appoint_id` int NOT NULL COMMENT '预约ID',
  `diagnose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `u_disease` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户疾病诊断',
  `u_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户病情描述',
  `u_report_imgs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户报告',
  `d_disease` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生疾病诊断',
  `d_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '医生病情描述',
  `d_treat_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '治疗计划',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`appoint_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '手术预约诊断' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_backmoney
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_backmoney`;
CREATE TABLE `api_clinic_backmoney`  (
  `backmoney_id` int NOT NULL AUTO_INCREMENT,
  `clinic_id` int NULL DEFAULT 0 COMMENT '诊所ID',
  `clinic_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '诊所名称',
  `appoint_order_id` int NULL DEFAULT 0 COMMENT '预约ID',
  `appoint_pay_id` int NULL DEFAULT 0 COMMENT 'payID',
  `appoint_order_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `appoint_username` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '预约人姓名',
  `sch_date` date NULL DEFAULT NULL COMMENT '预约日期',
  `backmoney_reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '退款原因',
  `apply_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请时间',
  `apply_status` tinyint(1) NULL DEFAULT 0 COMMENT '退款状态1已退款0未退款',
  `kf_check` tinyint(1) NOT NULL DEFAULT 0 COMMENT '客服人员是否已检查',
  `update_time` int NULL DEFAULT 0 COMMENT '退款时间',
  `admin_id` int NULL DEFAULT 0 COMMENT '退款管理员',
  PRIMARY KEY (`backmoney_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49248 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户退款申请表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_cancellog
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_cancellog`;
CREATE TABLE `api_clinic_cancellog`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `appoint_order_num` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `cancel_type` int NOT NULL DEFAULT 1 COMMENT '1ajax撤销 2基础init监测未支付撤销 3linux定时',
  `create_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_depart`;
CREATE TABLE `api_clinic_depart`  (
  `department_id` int NOT NULL AUTO_INCREMENT,
  `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `department_sort` int NULL DEFAULT 0 COMMENT '科室排序',
  `department_clinic_id` int NOT NULL,
  `department_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '科室状态',
  `department_sortCopy` int NULL DEFAULT 0 COMMENT '科室排序',
  PRIMARY KEY (`department_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10142 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '诊所科室表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_depart_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_depart_relation`;
CREATE TABLE `api_clinic_depart_relation`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `search_depart_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '搜索科室id',
  `clinic_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院id',
  `small_system` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1小系统0诊所',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序大到小',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 88 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_doctor
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_doctor`;
CREATE TABLE `api_clinic_doctor`  (
  `doctor_id` int NOT NULL AUTO_INCREMENT,
  `doc_code` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `doctor_clinic_id` int NULL DEFAULT NULL COMMENT '诊所id',
  `doctor_department_id` int NULL DEFAULT NULL COMMENT '科室id',
  `doctor_good_at` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '擅长',
  `doctor_sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `doctor_detail_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '详细信息',
  `doctor_special_info` varchar(225) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生特色',
  `doctor_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `doctor_headerimg_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `doctor_sex` tinyint(1) NULL DEFAULT 1 COMMENT '性别',
  `doctor_duty_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊信息',
  `doctor_duty_conf` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生值班规律配置',
  `doctor_profession_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生职称',
  `doctor_status` tinyint(1) NULL DEFAULT 1 COMMENT '医生状态',
  `appoint_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '挂号费',
  `doctor_tag` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '医生标签',
  `appoint_days` int UNSIGNED NULL DEFAULT NULL COMMENT '提前预约天数',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '标签组ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组内排序号',
  `doc_type` tinyint NOT NULL DEFAULT 1 COMMENT '医生类型',
  `ask_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '在线问诊跳转链接',
  `work_year` int NOT NULL DEFAULT 0 COMMENT '开始工作年',
  `jz_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊地址',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就医备注',
  `create_time` int NULL DEFAULT NULL,
  `is_show` int NOT NULL DEFAULT 1,
  `doc_sort` int NOT NULL DEFAULT 255,
  `id` int NOT NULL,
  PRIMARY KEY (`doctor_id`) USING BTREE,
  INDEX `api_clinic_doctor_doctor_clinic_id_IDX`(`doctor_clinic_id` ASC, `doctor_department_id` ASC, `doctor_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10897 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '诊所医生表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_doctor_operation
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_doctor_operation`;
CREATE TABLE `api_clinic_doctor_operation`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `clinic_id` int NOT NULL COMMENT '诊所ID',
  `clinic_dep_id` int NOT NULL COMMENT '科室ID',
  `doctor_id` int NOT NULL COMMENT '医师ID',
  `operation_id` int NOT NULL COMMENT '手术ID',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '手术价格',
  `sort` int NOT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生手术表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_doctor_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_doctor_rule`;
CREATE TABLE `api_clinic_doctor_rule`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int UNSIGNED NULL DEFAULT NULL,
  `clinic_id` int UNSIGNED NULL DEFAULT NULL COMMENT '诊所ID',
  `clinic_dep_id` int UNSIGNED NULL DEFAULT NULL COMMENT '科室id',
  `doctor_id` int UNSIGNED NULL DEFAULT 0 COMMENT '医生id',
  `on_week` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '周几',
  `on_time_type` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间类型',
  `on_time_str` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '具体时间段',
  `max_treat_num` int UNSIGNED NULL DEFAULT 0 COMMENT '号源量',
  `appoint_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `appoint_days` int UNSIGNED NULL DEFAULT 0 COMMENT '提前预约天数',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '状态',
  `sort` int UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NULL DEFAULT 0 COMMENT '修改时间',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2627 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_doctor_rule_copy
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_doctor_rule_copy`;
CREATE TABLE `api_clinic_doctor_rule_copy`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int UNSIGNED NULL DEFAULT NULL,
  `clinic_id` int UNSIGNED NULL DEFAULT NULL COMMENT '诊所ID',
  `clinic_dep_id` int UNSIGNED NULL DEFAULT NULL COMMENT '科室id',
  `doctor_id` int UNSIGNED NULL DEFAULT 0 COMMENT '医生id',
  `on_week` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '周几',
  `on_time_type` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间类型',
  `on_time_str` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '具体时间段',
  `max_treat_num` int UNSIGNED NULL DEFAULT 0 COMMENT '号源量',
  `appoint_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `appoint_days` int UNSIGNED NULL DEFAULT 0 COMMENT '提前预约天数',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '状态',
  `sort` int UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NULL DEFAULT 0 COMMENT '修改时间',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1814 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_expert
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_expert`;
CREATE TABLE `api_clinic_expert`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `title` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签',
  `good_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '擅长',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序倒叙',
  `search_depart_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '搜索科室id',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转地址',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1启动-1禁用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_operation
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_operation`;
CREATE TABLE `api_clinic_operation`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `clinic_id` int NOT NULL COMMENT '医院ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手术名称',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频地址',
  `introduce` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '总结描述',
  `sort` int NOT NULL DEFAULT 0,
  `status` int NOT NULL DEFAULT 0 COMMENT '状态',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '手术预约' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_pay
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_pay`;
CREATE TABLE `api_clinic_pay`  (
  `appoint_pay_id` int NOT NULL AUTO_INCREMENT,
  `appoint_order_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约订单号',
  `total_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '总价格',
  `real_pay_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际支付金额',
  `wx_pay_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付流水号',
  `pay_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付时间',
  `check_status` tinyint(1) NOT NULL DEFAULT -1 COMMENT '是否已人工审核',
  PRIMARY KEY (`appoint_pay_id`) USING BTREE,
  INDEX `i_appoint_order_num`(`appoint_order_num` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 330143 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预约支付信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_refundlog
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_refundlog`;
CREATE TABLE `api_clinic_refundlog`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款操作时间',
  `admin_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员主键',
  `backmoney_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款表主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4272 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_clinic_schedule
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_schedule`;
CREATE TABLE `api_clinic_schedule`  (
  `schedule_id` int NOT NULL AUTO_INCREMENT,
  `doctor_rule_id` int NULL DEFAULT NULL COMMENT '医生坐诊ID',
  `user_id` int NULL DEFAULT NULL,
  `clinic_id` int NULL DEFAULT NULL,
  `department_id` int NOT NULL,
  `doctor_id` int NOT NULL COMMENT '医生id',
  `treat_date` date NOT NULL COMMENT '接诊日期',
  `treat_date_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '初诊日类型 2上午 3下午',
  `treat_time_str` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊具体时间',
  `max_treat_num` int NOT NULL DEFAULT 1 COMMENT '最大接诊量',
  `treated_num` int NOT NULL DEFAULT 0 COMMENT '已预约量',
  `treat_fee` decimal(10, 2) NOT NULL DEFAULT 10.00 COMMENT '挂号费',
  `treat_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '接诊状态 1预约 0停诊 -2放假',
  `create_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`schedule_id`) USING BTREE,
  INDEX `rds_idx_0`(`doctor_id` ASC, `treat_date` ASC) USING BTREE,
  INDEX `api_clinic_schedule_treat_date_IDX`(`treat_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 216946 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生排班表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_schedule_copy
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_schedule_copy`;
CREATE TABLE `api_clinic_schedule_copy`  (
  `schedule_id` int NOT NULL AUTO_INCREMENT,
  `doctor_id` int NOT NULL COMMENT '医生id',
  `treat_date` date NOT NULL COMMENT '接诊日期',
  `treat_date_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '初诊日类型 2上午 3下午',
  `treat_time_str` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊具体时间',
  `max_treat_num` int NOT NULL DEFAULT 1 COMMENT '最大接诊量',
  `treated_num` int NOT NULL DEFAULT 0 COMMENT '已预约量',
  `treat_fee` decimal(10, 2) NOT NULL DEFAULT 10.00 COMMENT '挂号费',
  `treat_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '接诊状态',
  PRIMARY KEY (`schedule_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30348 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生排班表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_schedule_copy1
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_schedule_copy1`;
CREATE TABLE `api_clinic_schedule_copy1`  (
  `schedule_id` int NOT NULL AUTO_INCREMENT,
  `doctor_id` int NOT NULL COMMENT '医生id',
  `treat_date` date NOT NULL COMMENT '接诊日期',
  `treat_date_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '初诊日类型 2上午 3下午',
  `treat_time_str` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊具体时间',
  `max_treat_num` int NOT NULL DEFAULT 1 COMMENT '最大接诊量',
  `treated_num` int NOT NULL DEFAULT 0 COMMENT '已预约量',
  `treat_fee` decimal(10, 2) NOT NULL DEFAULT 10.00 COMMENT '挂号费',
  `treat_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '接诊状态',
  PRIMARY KEY (`schedule_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30336 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生排班表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_clinic_search_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_clinic_search_depart`;
CREATE TABLE `api_clinic_search_depart`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `dep_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室名',
  `dep_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室说明',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序,大到小',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '1启用-1禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创新时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_code
-- ----------------------------
DROP TABLE IF EXISTS `api_code`;
CREATE TABLE `api_code`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '二维码id',
  `code_scene` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码参数',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '二维码类型',
  `scene` tinyint(1) NOT NULL COMMENT '二维码场景（0-临时，1-活动，2-推广）',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '二维码创建时间',
  `detail` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码用途备注',
  `url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码url地址',
  `code_ticket` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码ticket',
  `code_path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码储存地址',
  `owner` int NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '二维码状态（0删除，1可用，3过期）',
  `update_time` int NULL DEFAULT 0 COMMENT '更新时间',
  `copy` tinyint(1) NULL DEFAULT 0 COMMENT '复制处理',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code_scene`(`code_scene` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 502 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '场景二维码信息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_code_0511bak
-- ----------------------------
DROP TABLE IF EXISTS `api_code_0511bak`;
CREATE TABLE `api_code_0511bak`  (
  `id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '二维码id',
  `code_scene` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码参数',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '二维码类型',
  `scene` tinyint(1) NOT NULL COMMENT '二维码场景（0-临时，1-活动，2-推广）',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '二维码创建时间',
  `detail` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码用途备注',
  `url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码url地址',
  `code_ticket` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码ticket',
  `code_path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码储存地址',
  `owner` int NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '二维码状态（0删除，1可用，3过期）',
  `update_time` int NULL DEFAULT 0 COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_consultation_info
-- ----------------------------
DROP TABLE IF EXISTS `api_consultation_info`;
CREATE TABLE `api_consultation_info`  (
  `appoint_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '挂号id',
  `consultation_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会诊内内容',
  `consultation_imgs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会诊图',
  `consultation_zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会诊压缩包',
  `user_info_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `hos_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `other_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '说明信息',
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1673 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_coupon
-- ----------------------------
DROP TABLE IF EXISTS `api_coupon`;
CREATE TABLE `api_coupon`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠码',
  `total` decimal(10, 2) NOT NULL COMMENT '总金额',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态:0未使用,1已使用',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `user_id` int NOT NULL DEFAULT 0,
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2584 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_covid19_confirm
-- ----------------------------
DROP TABLE IF EXISTS `api_covid19_confirm`;
CREATE TABLE `api_covid19_confirm`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省内居住地',
  `address_detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `live_type` enum('其他','工地宿舍','宿舍','酒店','投亲靠友','租住','自住') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '居住类型',
  `is_other_area` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '去过其他地区:0=否,1=是',
  `is_high_area` enum('0','2','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '去过高风险:0=未去过中高风险地区,1=去过中风险地区,2=去过高风险地区',
  `is_covid` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否新冠症状:0=否,1=是',
  `temperature` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '体温小于37:0=正常,1=发烧',
  `health_card_color` enum('2','1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '健康码:0=绿码,1=黄码,2=宏妈',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=无效,1=有效',
  `patient_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊人身份证',
  `patient_phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者手机号',
  `patient_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者名字',
  `hos_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `from_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道',
  `is_contact_days` enum('0','1','-1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '14天内新型,疑似感染者接触史:0=否,1=是',
  `is_gather` enum('0','1','-1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否接触过聚集患者或场所:0=否,1=是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patientid_hoscode_status`(`patient_id` ASC, `hos_code` ASC, `status` ASC) USING BTREE,
  INDEX `idx_patientphone_createtime`(`patient_phone` ASC, `create_time` ASC) USING BTREE COMMENT 'create by DAS-3d1a46cf-08e3-47d1-a834-8624fdcbfec9',
  INDEX `idx_patientidcard_createtime`(`patient_id_card` ASC, `create_time` ASC) USING BTREE COMMENT 'create by DAS-3a7fb4f6-d9f3-40f4-a4ae-115eb713db18'
) ENGINE = InnoDB AUTO_INCREMENT = 4701744 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新冠承诺' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_covid19_create
-- ----------------------------
DROP TABLE IF EXISTS `api_covid19_create`;
CREATE TABLE `api_covid19_create`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `bill_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开单id',
  `hos_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院code',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '滇医通就诊人id',
  `jz_card` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊卡',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0单检1混检',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_covid19_create_create_time_IDX`(`create_time` ASC) USING BTREE,
  INDEX `api_covid19_create_hos_id_IDX`(`hos_id` ASC) USING BTREE,
  INDEX `api_covid19_create_bill_id_IDX`(`bill_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2237673 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_cschedule_record
-- ----------------------------
DROP TABLE IF EXISTS `api_cschedule_record`;
CREATE TABLE `api_cschedule_record`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0失败1成功',
  `info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '生成工作安排的说明',
  `clinic_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院表主键',
  `clinic_dep_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '科室表主键',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生表主键',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27817 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dep_area
-- ----------------------------
DROP TABLE IF EXISTS `api_dep_area`;
CREATE TABLE `api_dep_area`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `area_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '区域名称',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '0禁用1启用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `hospital_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `building_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼id',
  `floor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼层表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 445 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院某楼楼层区域表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dep_building
-- ----------------------------
DROP TABLE IF EXISTS `api_dep_building`;
CREATE TABLE `api_dep_building`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hospital_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `building_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院XX大楼',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1启用0禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 99 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院大楼' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dep_department
-- ----------------------------
DROP TABLE IF EXISTS `api_dep_department`;
CREATE TABLE `api_dep_department`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `department_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室名字',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1启用0禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `hospital_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `building_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼id',
  `floor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼层表id',
  `area_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼层区域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2826 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院部门' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dep_floor
-- ----------------------------
DROP TABLE IF EXISTS `api_dep_floor`;
CREATE TABLE `api_dep_floor`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hospital_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `floor_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '楼层名',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1启用0禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `building_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联医院楼id',
  `has_area` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否分区域',
  `floor_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '楼层图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 726 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院某楼楼层表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_depart`;
CREATE TABLE `api_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `dep_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `first_char` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室首字母',
  `dep_intro` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室简介',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室地址',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_recomm` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `ask_dep_id` int NOT NULL DEFAULT 0 COMMENT '在线问诊关联科室ID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
  `one_dept_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室code',
  `one_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室名称',
  `two_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二级科室名称',
  `dep_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室弹窗提醒',
  `two_dept_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_hd`(`hos_id` ASC, `dep_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 156982 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方科室信息完善表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_depart_0511bak
-- ----------------------------
DROP TABLE IF EXISTS `api_depart_0511bak`;
CREATE TABLE `api_depart_0511bak`  (
  `id` int NOT NULL DEFAULT 0,
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `dep_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `first_char` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室首字母',
  `dep_intro` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室简介',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室地址',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_recomm` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_depart_classify
-- ----------------------------
DROP TABLE IF EXISTS `api_depart_classify`;
CREATE TABLE `api_depart_classify`  (
  `id` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `depart_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类科室名称',
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '状态1启用0禁用-1删除',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `is_special` tinyint NOT NULL DEFAULT 0 COMMENT '是否为专病专诊首页推荐科室',
  `logo_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_depart_yunben
-- ----------------------------
DROP TABLE IF EXISTS `api_depart_yunben`;
CREATE TABLE `api_depart_yunben`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `dep_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `first_char` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室首字母',
  `dep_intro` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室简介',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室地址',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_recomm` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `ask_dep_id` int NOT NULL DEFAULT 0 COMMENT '在线问诊关联科室ID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
  `one_dept_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室code',
  `one_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室名称',
  `two_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二级科室名称',
  `dep_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室弹窗提醒',
  `two_dept_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_hd`(`hos_id` ASC, `dep_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 156735 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方科室信息完善表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_depart_yunben_cate
-- ----------------------------
DROP TABLE IF EXISTS `api_depart_yunben_cate`;
CREATE TABLE `api_depart_yunben_cate`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `dep_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类科室名',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '0禁用1启用',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '越大越靠前',
  `dep_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室id',
  `pid` int UNSIGNED NOT NULL DEFAULT 0,
  `doc_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '相关医师id',
  `dep_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提示信息',
  `name_ext` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室扩展',
  `level` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '方便搜索，第几级',
  `from_dyt` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '标识该科室属于滇医通管理',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转地址',
  `dep_remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `hos_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1236 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dict_value
-- ----------------------------
DROP TABLE IF EXISTS `api_dict_value`;
CREATE TABLE `api_dict_value`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型名',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编码',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 575 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_disease
-- ----------------------------
DROP TABLE IF EXISTS `api_disease`;
CREATE TABLE `api_disease`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `syskey` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '39key',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '疾病名称',
  `pinyin` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '39健康网的url中的一部分',
  `summary` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '简介',
  `medical_insurance` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否属于医保',
  `nickname` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '别名',
  `disease_location` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发病部位',
  `infectivity` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '传染性',
  `forobject` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '多发人群',
  `related_symptoms` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关症状',
  `complicated_disease` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '并发疾病',
  `visiting_department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '就诊科室',
  `treatment_cost` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '治疗费用',
  `treatment_rate` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '治愈率',
  `treatment_cycle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '治疗周期',
  `treatment_method` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '治疗方法',
  `treatment_check` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关检查',
  `treatment_drug` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '常用药品',
  `treatment_besttime` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最佳就诊时间',
  `treatment_whenlong` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊时长',
  `treatment_revisit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '复诊频率诊疗周期',
  `treatment_preparation` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊前准备',
  `cause_main` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主要病因',
  `cause_detail` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '疾病详细病因',
  `symptom_typical` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '典型症状',
  `symptom_early` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `symptom_later` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `symptom_other` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关症状',
  `symptom_diagnosis` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '症状诊断',
  `treatment_info` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '治疗方法详细',
  `eating_allow` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '饮食适宜',
  `eating_deny` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '饮食禁忌',
  `eating_principle` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '饮食原则',
  `prevention_methods` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '预防方法',
  `collect_info_time` int NOT NULL DEFAULT 0,
  `collect_jbzs_time` int NOT NULL DEFAULT 0,
  `collect_blby_time` int NOT NULL DEFAULT 0,
  `collect_zztz_time` int NOT NULL DEFAULT 0,
  `collect_yyzl_time` int NOT NULL DEFAULT 0,
  `collect_ysbj_time` int NOT NULL DEFAULT 0,
  `collect_yfhl_time` int NOT NULL DEFAULT 0,
  `collect_summary_time` int NULL DEFAULT 0 COMMENT '采集简介时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `syskey`(`syskey` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7060 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '疾病表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_doc_group_ser_cate
-- ----------------------------
DROP TABLE IF EXISTS `api_doc_group_ser_cate`;
CREATE TABLE `api_doc_group_ser_cate`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务名',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '信息',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '-1禁用0开发1启用',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者 0系统',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人 0系统',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `cate` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1基础包2服务包',
  `has_nums` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '次数',
  `has_times` int UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '为问诊团队创建的服务表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doc_group_server
-- ----------------------------
DROP TABLE IF EXISTS `api_doc_group_server`;
CREATE TABLE `api_doc_group_server`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `ser_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '服务主键 1在线问诊 2电话问诊',
  `ser_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务名称 1在线问诊 2电话问诊',
  `ser_price` decimal(10, 2) NOT NULL,
  `ser_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务说明',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '1启用 0开发 -1 禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人 0 系统',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间 0系统',
  `doc_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队关联',
  `has_nums` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '能够问几次',
  `has_times` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '能问多长时间',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '越大越靠前',
  `cate` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0默认值1基础服务2服务包',
  `greetings` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接单后问候语',
  `max_num` int NOT NULL DEFAULT 0 COMMENT '单日接诊量0为无限制',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doc_group_id`(`doc_group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5069 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医师团服务表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor`;
CREATE TABLE `api_doctor`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_good` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `rules` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约规则',
  `special_info` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '特殊说明',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `doc_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '医生标签ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组排序号',
  `doc_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `doc_class_id` int NULL DEFAULT 0 COMMENT '科室分类ID',
  `doc_address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `resource_type` tinyint(1) NULL DEFAULT 1 COMMENT '类型 1：医生 2：门诊',
  `is_show` int NOT NULL DEFAULT 1,
  `doc_sort` int NOT NULL DEFAULT 255 COMMENT '医生主页排序',
  `sort` int NOT NULL DEFAULT 255 COMMENT '挂号页面排序',
  `doctor_duty_info` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊信息',
  `reservation_rules` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预约规则',
  `is_multidisciplinary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否多学科医生0否1是',
  `hospital_appt_cate_dep_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院亚专业分类ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_dep_id`(`dep_id` ASC) USING BTREE,
  INDEX `index_doc_id`(`doc_id` ASC) USING BTREE,
  INDEX `api_doctor_hos_id_IDX`(`hos_id` ASC, `dep_id` ASC, `doc_id` ASC) USING BTREE,
  INDEX `api_doctor_doc_name_IDX`(`doc_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 94292 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_doctor_0110bak
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_0110bak`;
CREATE TABLE `api_doctor_0110bak`  (
  `id` int NOT NULL DEFAULT 0,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `rules` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约规则',
  `special_info` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '特殊说明',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `doc_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '医生标签ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组排序号'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_0318bak
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_0318bak`;
CREATE TABLE `api_doctor_0318bak`  (
  `id` int NOT NULL DEFAULT 0,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `rules` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约规则',
  `special_info` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '特殊说明',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `doc_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '医生标签ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组排序号'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_871058tem
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_871058tem`;
CREATE TABLE `api_doctor_871058tem`  (
  `id` int NOT NULL DEFAULT 0,
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `doc_avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生姓名',
  `level_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生职称',
  `doc_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `doc_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `rules` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约规则',
  `special_info` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '特殊说明',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `doc_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '医生标签ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组排序号',
  `doc_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `doc_address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_arrange
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_arrange`;
CREATE TABLE `api_doctor_arrange`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院主键',
  `depart_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属科室主键',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生主键',
  `on_week` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '周几',
  `on_time_type` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'am/pm',
  `on_time_str` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '8:00-12:00',
  `max_treat_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '治疗人数',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `is_datepart` tinyint NOT NULL DEFAULT 0 COMMENT '是否分时段排班',
  `datepart` int NOT NULL DEFAULT 0 COMMENT '切割的小时段时间',
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `operator_id` int NULL DEFAULT NULL COMMENT '修改人id',
  `create_operator_id` int NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doctor_id`(`doctor_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 197727 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生排班表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_bind
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_bind`;
CREATE TABLE `api_doctor_bind`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '模型',
  `record_id` int NOT NULL DEFAULT 0 COMMENT '记录ID',
  `record_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '关联记录标题',
  `extension` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展内容',
  `hospital_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '医院类型',
  `hospital_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `depart_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室编码',
  `doctor_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生编码',
  `hospital_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `depart_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `doctor_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生名称',
  `doctor_avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '医生头像',
  `level_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '职称',
  `create_time` int NULL DEFAULT 0 COMMENT '创建时间',
  `create_id` int NULL DEFAULT NULL,
  `create_operator` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5430 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_doctor_comment
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_comment`;
CREATE TABLE `api_doctor_comment`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `order_id` int NOT NULL,
  `hos_type` int NOT NULL COMMENT ' 医院类型（1：公立，2：私立，3：诊所） ',
  `hos_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院编码',
  `dep_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室编码',
  `doc_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生编码',
  `whole_score` int NOT NULL DEFAULT 0 COMMENT '整体评价评分',
  `hos_score` int NOT NULL COMMENT '医院评分',
  `doc_score` int NOT NULL COMMENT '医生评分',
  `doc_skill_score` int NOT NULL DEFAULT 0 COMMENT '医生专业技能',
  `worker_score` int NOT NULL DEFAULT 0 COMMENT '医务工作人员评分',
  `nurse_score` int NOT NULL COMMENT '护士医务人员评分',
  `guider_score` int NOT NULL DEFAULT 0 COMMENT '导医态度评分',
  `register_score` int NOT NULL DEFAULT 0 COMMENT '挂号等候时间评分',
  `visit_score` int NOT NULL COMMENT '候诊等候时间评分',
  `check_score` int NOT NULL DEFAULT 0 COMMENT '检查等候时间评分',
  `drug_score` int NOT NULL DEFAULT 0 COMMENT '取药等候时间',
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生评价',
  `hos_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院评价',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态（0：不显示，1：显示）',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  `server_attitude` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '服务态度',
  `server_quality` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '服务质量',
  `server_result` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '服务效果',
  `satisfaction` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '满意程度',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index1`(`user_id` ASC, `order_id` ASC) USING BTREE,
  INDEX `idx_orderid_docid`(`order_id` ASC, `doc_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 119025 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_group
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_group`;
CREATE TABLE `api_doctor_group`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师名称',
  `tag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '团队标签',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提问价格',
  `get_money` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '能否提现 1可以0不可以',
  `hospital_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院类型1：公立HIS，2：专科，3：诊所',
  `hospital_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `hospital_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `depart_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室编码',
  `depart_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `doctor_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生编码',
  `doctor_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生姓名',
  `good_at` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '擅长',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生介绍',
  `question_tmpl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题模板json',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态1启用0禁用',
  `admin_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队负责人',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序默认0，升序',
  `follow_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注数默认0',
  `qr_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频地址',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者用户ID',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `question_base` int NOT NULL DEFAULT 0 COMMENT '提问基数',
  `question_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '提问次数',
  `video_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频个数',
  `work_time_desc` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接诊时间(时间段描述)',
  `auto_refund_exp` int NOT NULL DEFAULT 24 COMMENT '医师未回答自动退款期效',
  `auto_close_exp` int NOT NULL DEFAULT 24 COMMENT '关闭问题期效',
  `max_ask_times` int NOT NULL DEFAULT 2 COMMENT '允许患者追问次数',
  `s_subdepart` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '子科室',
  `s_depart` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '搜索的科室id',
  `work_year` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '开始工作年',
  `rate_income` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题分成比例 0-100',
  `question_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题标签',
  `group_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组宣传图',
  `home_recommend` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否首页推荐',
  `home_sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '首页推荐排序',
  `dyt_admin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '滇医通用户',
  `active_sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '昨日完成量',
  `notice_start` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知开始时间',
  `notice_end` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知结束时间',
  `comment_num` int NOT NULL DEFAULT 0 COMMENT '评价数',
  `good_comment_rate` float(6, 2) NOT NULL DEFAULT 0.00 COMMENT '好评率',
  `comment_score` float(6, 2) NOT NULL DEFAULT 0.00 COMMENT '评论综合评分',
  `avg_answer_time` int NOT NULL DEFAULT 0 COMMENT '平均等待接单时间(单位:分)',
  `pay_clinic` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1开启付费 0 关闭付费',
  `free_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '免费说明',
  `free_clinic` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0不1义诊',
  `free_clinic_start_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '义诊开始时间',
  `free_clinic_end_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '义诊结束时间',
  `first_free` tinyint NOT NULL DEFAULT 0 COMMENT '是否首次免费0否1是',
  `first_free_start_time` int NULL DEFAULT NULL COMMENT '免费开始时间',
  `first_free_end_time` int NULL DEFAULT NULL COMMENT '免费结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_doctor_group_admin_user_id_IDX`(`admin_user_id` ASC) USING BTREE,
  FULLTEXT INDEX `ask_doc_ft_index`(`name`, `tag`, `hospital_name`, `depart_name`, `good_at`) COMMENT '在线问诊医生全文索引'
) ENGINE = InnoDB AUTO_INCREMENT = 5223 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医师表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_doctor_income
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_income`;
CREATE TABLE `api_doctor_income`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `ask_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ask_order表order_no',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'questions 表doctor_group_user_id',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '收入费用	',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题id',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未提现   1已提现',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1提现记录  0收入记录',
  `tx_order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '提现订单号',
  `doctor_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_doctorid_status_type_updatetime_price`(`doctor_id` ASC, `status` ASC, `type` ASC, `update_time` ASC, `price` ASC) USING BTREE,
  INDEX `idx_ask_order_no`(`ask_order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1020639 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医师收入表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_patient_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_patient_relation`;
CREATE TABLE `api_doctor_patient_relation`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户IDwechat_id',
  `wechat_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'wx id',
  `wechat_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者头像',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者ID',
  `doc_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问诊组id',
  `doc_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生ID',
  `doc_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师头像',
  `doc_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师名',
  `doc_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `doc_hospital_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师医院',
  `doc_depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师科室',
  `doc_unique_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '唯一id',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态,-2:删除,-1:已屏蔽,0:申请待审核,1:审核通过',
  `tag_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '标签id分组',
  `is_important` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否重点关注',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '申请时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `accept_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请通过时间',
  `show` tinyint NOT NULL DEFAULT 0 COMMENT '聊天页是否显示',
  `has_nums` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '剩余次数',
  `over_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '停止聊天时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医患关系表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_patient_report
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_patient_report`;
CREATE TABLE `api_doctor_patient_report`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `relation_id` int UNSIGNED NOT NULL COMMENT '关系主键',
  `last_jz_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后就诊时间',
  `disease` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '什么病',
  `type` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '就诊类型,1:门诊患者,2:住院患者',
  `treat_plan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '诊疗建议',
  `in_hospital` int NOT NULL DEFAULT 0 COMMENT '是否在院:-1出院,1在院',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者报到信息' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_patient_set
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_patient_set`;
CREATE TABLE `api_doctor_patient_set`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `doc_user_id` int NULL DEFAULT 0 COMMENT '医生ID',
  `check_type` int UNSIGNED NULL DEFAULT 1 COMMENT '审核方式:1:手动,2:自动',
  `report_ask_num` int UNSIGNED NULL DEFAULT 0 COMMENT '报到后赠送问诊次数',
  `visit_ask_num` int UNSIGNED NULL DEFAULT 0 COMMENT '随访赠送问诊次数',
  `create_time` int UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doc_user_id_index`(`doc_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者报到医生端设置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_recommend
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_recommend`;
CREATE TABLE `api_doctor_recommend`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生ID',
  `dep_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '科室ID',
  `doc_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生ID',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_hos_id`(`hos_id` ASC) USING BTREE,
  INDEX `i_dep_id`(`dep_id` ASC) USING BTREE,
  INDEX `i_doc_id`(`doc_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生推荐' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_says
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_says`;
CREATE TABLE `api_doctor_says`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `says` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '常用语',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '团队主键',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者id',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3165 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_stop
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_stop`;
CREATE TABLE `api_doctor_stop`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hos_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院编码',
  `sch_date` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '停诊日期',
  `stop_type` tinyint NOT NULL COMMENT '停诊类型 1: 小系统 2:诊所',
  `dep_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室编码',
  `doc_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `time_type` tinyint NULL DEFAULT NULL COMMENT ' 班次',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_doctor_stop_sch_date_IDX`(`sch_date` ASC, `hos_code` ASC, `dep_id` ASC, `doc_id` ASC, `time_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6030 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_symptom
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_symptom`;
CREATE TABLE `api_doctor_symptom`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '症状名称',
  `sort` int NOT NULL COMMENT '排序',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `doctor_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生ids',
  `hos_id` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0 COMMENT '0禁用1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '症状医生' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_tags
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_tags`;
CREATE TABLE `api_doctor_tags`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生标签表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_vip_class
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_vip_class`;
CREATE TABLE `api_doctor_vip_class`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'vip医生分类表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_work
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_work`;
CREATE TABLE `api_doctor_work`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生id',
  `max_treat` smallint UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大治疗人数',
  `used_treat` smallint UNSIGNED NOT NULL DEFAULT 0 COMMENT '已经预约数量',
  `work_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '工作日期',
  `work_time_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'am/pm',
  `work_time_str` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '时间段',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态1开诊0停诊',
  `sort` int UNSIGNED NOT NULL DEFAULT 255,
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院表主键',
  `depart_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '科室表主键',
  `is_datepart` tinyint NULL DEFAULT 0 COMMENT '是否分时段排班',
  `datepart` int NULL DEFAULT 0 COMMENT '分时段排班段(分钟)',
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `create_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `operator_id` int NULL DEFAULT NULL COMMENT '修改人id',
  `create_operator_id` int NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doctor_id`(`doctor_id` ASC) USING BTREE,
  INDEX `api_doctor_work_work_time_IDX`(`work_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1991607 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生工作表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_doctor_work_datepart
-- ----------------------------
DROP TABLE IF EXISTS `api_doctor_work_datepart`;
CREATE TABLE `api_doctor_work_datepart`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `schedule_id` int UNSIGNED NOT NULL COMMENT '排班id',
  `hospital_id` int NOT NULL COMMENT '医院id',
  `depart_id` int NOT NULL COMMENT '部门id',
  `doctor_id` int NOT NULL COMMENT '医生id',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  `sch_date` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊时间',
  `queue_sn` int NOT NULL COMMENT '序号',
  `start_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开始时间',
  `end_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间',
  `has_use` tinyint NOT NULL COMMENT '是否预约',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1: 开诊 0:停诊',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn_unique`(`schedule_id` ASC, `queue_sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 683672 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生工作分时段表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dyt_2021_11_4
-- ----------------------------
DROP TABLE IF EXISTS `api_dyt_2021_11_4`;
CREATE TABLE `api_dyt_2021_11_4`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `two` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Two',
  `one` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'One',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 520 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dyt_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_dyt_depart`;
CREATE TABLE `api_dyt_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院编码',
  `dep_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室编码',
  `dep_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 89 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '滇医通大医院科室' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_dyt_doctor
-- ----------------------------
DROP TABLE IF EXISTS `api_dyt_doctor`;
CREATE TABLE `api_dyt_doctor`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dyt_dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '滇医通科室ID',
  `hos_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生ID',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生姓名',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_hos_id`(`hos_id` ASC) USING BTREE,
  INDEX `index_dep_id`(`dep_id` ASC) USING BTREE,
  INDEX `index_doc_id`(`doc_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '滇医通医生信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_exception_log
-- ----------------------------
DROP TABLE IF EXISTS `api_exception_log`;
CREATE TABLE `api_exception_log`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联appoint_record',
  `order_event` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单事件',
  `level` tinyint NULL DEFAULT 0 COMMENT '异常等级',
  `event_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1用户2定时3系统',
  `event_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细描述问题',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未查看',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 337447 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付异常表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_exception_refund
-- ----------------------------
DROP TABLE IF EXISTS `api_exception_refund`;
CREATE TABLE `api_exception_refund`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付流水表的order_no',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作员id',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未退款1已退款',
  `handle_times` int NOT NULL DEFAULT 0 COMMENT '异常处理操作次数',
  `refund_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_orderno`(`order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75694 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '退款记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_ext_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_ext_depart`;
CREATE TABLE `api_ext_depart`  (
  `id` int NOT NULL,
  `hos_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院ID',
  `ext_dep_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展科室ID',
  `ext_dep_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展科室名称',
  `first_char` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '首字母',
  `ext_dep_intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展科室介绍',
  `ext_dep_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展科室地址',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父扩展科室ID',
  `ext_dep_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展科室弹窗提醒',
  `status` int NOT NULL DEFAULT 0 COMMENT '扩展科室状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_faq
-- ----------------------------
DROP TABLE IF EXISTS `api_faq`;
CREATE TABLE `api_faq`  (
  `faq_id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `cate_id` int NOT NULL,
  `faq_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `faq_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `faq_sort` int NOT NULL DEFAULT 0,
  `is_hot` tinyint(1) NOT NULL DEFAULT 0,
  `add_time` int NOT NULL,
  `update_time` int NOT NULL,
  `view_times` int NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`faq_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1040 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '问题详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_faq_category
-- ----------------------------
DROP TABLE IF EXISTS `api_faq_category`;
CREATE TABLE `api_faq_category`  (
  `cate_id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `cate_sort` int NOT NULL DEFAULT 0,
  `cate_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `cate_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `hos_code` int NOT NULL DEFAULT 0 COMMENT '医院编码 默认为0',
  `add_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `update_time` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`cate_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10012 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '问题分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_feedback
-- ----------------------------
DROP TABLE IF EXISTS `api_feedback`;
CREATE TABLE `api_feedback`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `openid` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户的openid',
  `title` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1咨询2建议3举报\r\n1功能故障2优化建议3订单投诉4其他',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '记录的时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '修改时间',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未处理1已处理',
  `images` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `order_type` int NULL DEFAULT 0 COMMENT '订单类型1挂号订单2问诊订单3缴费订单4t体检订单5就诊顾问订单',
  `order_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '订单id',
  `user_id` int NULL DEFAULT 0 COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35573 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '反馈 建议 举报 相关表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_files_patientinfo
-- ----------------------------
DROP TABLE IF EXISTS `api_files_patientinfo`;
CREATE TABLE `api_files_patientinfo`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `wechat_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '唯一标识',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者id',
  `patient_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊人名字',
  `patient_sex` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1男2女0未知',
  `patient_age` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '年龄',
  `patient_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '证件号',
  `patient_card_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0无卡1身份证',
  `bear_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '生育情况',
  `family_disease_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '家族病',
  `operation_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手术外伤',
  `drug_allergy_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '药物过敏',
  `food_allergy_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '食物过敏',
  `habit_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '行为习惯',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1有效0无效',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_files_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_files_tag`;
CREATE TABLE `api_files_tag`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `pid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'pid',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '越大越高',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1启用0未启用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '档案标签' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_focus
-- ----------------------------
DROP TABLE IF EXISTS `api_focus`;
CREATE TABLE `api_focus`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `position_id` int NOT NULL COMMENT '焦点图所属位置',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '焦点图标题',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '焦点图链接地址',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `remark` tinytext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '焦点图说明',
  `status` tinyint NOT NULL COMMENT '是否可用',
  `sort` int NOT NULL COMMENT '排序',
  `views` int NULL DEFAULT 0 COMMENT '点击浏览次数',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `position`(`position_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '焦点图' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_focus_position
-- ----------------------------
DROP TABLE IF EXISTS `api_focus_position`;
CREATE TABLE `api_focus_position`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '焦点图位置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_full_order
-- ----------------------------
DROP TABLE IF EXISTS `api_full_order`;
CREATE TABLE `api_full_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `patient_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人姓名',
  `jz_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡号',
  `zy_num` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '住院号',
  `full_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `his_order_no` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT 'his订单号',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `refund_time` int NOT NULL DEFAULT 0 COMMENT '退款时间',
  `status` int NOT NULL DEFAULT 0 COMMENT '订单状态0:待支付,1:支付成功,-1:微信支付成功his支付失败,-2:已退款',
  `res_msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结果信息',
  `type` int NOT NULL DEFAULT 0 COMMENT '类型:1-门诊充值,2-住院充值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19216 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门诊住院充值订单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_gongren_card
-- ----------------------------
DROP TABLE IF EXISTS `api_gongren_card`;
CREATE TABLE `api_gongren_card`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `reg_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sex` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sex_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `iden_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `patient_type_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `patient_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `udx_reg_no`(`reg_no` ASC) USING BTREE,
  INDEX `idx_iden_no`(`iden_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 251385 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工人医院就诊卡测试表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_config
-- ----------------------------
DROP TABLE IF EXISTS `api_home_config`;
CREATE TABLE `api_home_config`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor`;
CREATE TABLE `api_home_doctor`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agency_id` int NOT NULL COMMENT '所属机构ID',
  `head_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `doctor_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医生姓名',
  `good_at` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '擅长',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '简介',
  `reg_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '挂号医生链接',
  `ask_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '问诊医生链接',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 721 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_agency
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_agency`;
CREATE TABLE `api_home_doctor_agency`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `hospital_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院编码',
  `hospital_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院名称',
  `display_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '显示名称',
  `appoint_notice` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约须知',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `tag` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签多个都逗号隔开',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生机构' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_appoint
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_appoint`;
CREATE TABLE `api_home_doctor_appoint`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `hos_id` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院编码',
  `agency_id` int NOT NULL COMMENT '机构ID',
  `agency_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机构名称',
  `group_id` int NOT NULL COMMENT '团队ID',
  `group_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '团队名称',
  `order_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `amt` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int NOT NULL DEFAULT 0 COMMENT '就诊人ID',
  `patient_age` int NOT NULL COMMENT '患者年龄',
  `patient_sex` int NOT NULL COMMENT '患者性别 1-男 2-女',
  `patient_id_card` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '就诊人身份证号',
  `patient_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人手机号',
  `patient_address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '现住址',
  `chronic` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '慢病',
  `help_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '期望得到的帮助',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `order_status` int NOT NULL COMMENT '预约状态 0-待支付 1-预约成功 -1-已失效 2-已完成',
  `package_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务包ID多个用逗号隔开',
  `package_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务包名称多个用逗号隔开',
  `pay_status` int NULL DEFAULT NULL COMMENT '支付状态 1-已支付 -1-部分退款 -2-全额退款',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 618 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生预约' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_group
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_group`;
CREATE TABLE `api_home_doctor_group`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agency_id` int NOT NULL COMMENT '所属机构ID',
  `head_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `group_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '团队名称',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '简介',
  `group_tag` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '团队标签，英文逗号隔开',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 134 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生团队表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_package
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_package`;
CREATE TABLE `api_home_doctor_package`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agency_id` int NOT NULL COMMENT '所属机构ID',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '套餐名称',
  `classify` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类多个用逗号隔开',
  `check_package_id` int NULL DEFAULT NULL COMMENT '关联体检套餐',
  `tag` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签多个用逗号隔开',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 106 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生套餐' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_package_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_package_relation`;
CREATE TABLE `api_home_doctor_package_relation`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `package_id` int NOT NULL,
  `group_id` int NOT NULL COMMENT '团队ID',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 617 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生套餐团队关系' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_home_doctor_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_home_doctor_relation`;
CREATE TABLE `api_home_doctor_relation`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `group_id` int NOT NULL COMMENT '团队ID',
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `doctor_tag` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生标签',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 741 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '家庭医生团队绑定关系表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_homepage
-- ----------------------------
DROP TABLE IF EXISTS `api_homepage`;
CREATE TABLE `api_homepage`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `jump_url` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转地址',
  `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态1正常；隐藏',
  `sort` tinyint NULL DEFAULT NULL COMMENT '排序',
  `version` tinyint NULL DEFAULT NULL COMMENT '版本号:0老版本;1新版本',
  `position` tinyint NULL DEFAULT NULL COMMENT '所在位置',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hos_acc_data
-- ----------------------------
DROP TABLE IF EXISTS `api_hos_acc_data`;
CREATE TABLE `api_hos_acc_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院编码',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `transaction_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '支付订单号',
  `jz_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '就诊卡',
  `patient_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '就诊人',
  `amt` decimal(10, 2) NOT NULL COMMENT '金额',
  `type` int NOT NULL COMMENT '缴费项目(1:挂号,2:门诊缴费,预交款,住院结算)',
  `pay_status` int NOT NULL COMMENT '支付状态(1:已支付,-1:退费)',
  `trade_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交易时间',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 238684 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院账务数据表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hos_cate
-- ----------------------------
DROP TABLE IF EXISTS `api_hos_cate`;
CREATE TABLE `api_hos_cate`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院表主键',
  `cate_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型表主键',
  `sort` int NOT NULL DEFAULT 0 COMMENT '分类排序',
  `create_id` tinyint NOT NULL DEFAULT 52 COMMENT '创建者ID',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'C.x（超管）' COMMENT '创建者',
  `create_time` int NOT NULL DEFAULT 1524881686 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hos_id`(`hos_id` ASC) USING BTREE,
  INDEX `idx_cate_id`(`cate_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 667 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hos_shidiyi_order
-- ----------------------------
DROP TABLE IF EXISTS `api_hos_shidiyi_order`;
CREATE TABLE `api_hos_shidiyi_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `transaction_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付订单号',
  `jz_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `patient_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人',
  `amt` decimal(10, 2) NOT NULL COMMENT '金额',
  `type` int NOT NULL COMMENT '缴费项目(1:挂号,2:缴费,预交款,住院结算)',
  `pay_type` int NOT NULL COMMENT '支付类型(1:微信退款,2:支付宝支付,3银行卡支付)',
  `pay_status` int NOT NULL COMMENT '支付状态(1:已支付,2:取消支付,3:退费)',
  `pay_time` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付时间',
  `refund_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退费时间',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129160 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '市第一甘美对账订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital`;
CREATE TABLE `api_hospital`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hospital_logo` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '医院logo',
  `hospital_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '医院编号',
  `hospital_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院图片',
  `hospital_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `hospital_addr` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院地址',
  `hospital_longlat` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0,0' COMMENT '经纬度  ',
  `city_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属城市',
  `region_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '区域id',
  `level_id` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院等级',
  `hospital_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医院的简介',
  `hospital_rule` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医院相关规则',
  `hospital_phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院电话',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态  -1 开发中  0 维护   1启用  -2禁用',
  `is_onlinepay` tinyint NOT NULL DEFAULT 0 COMMENT '是否在线支付',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `floor_open` tinyint NOT NULL DEFAULT 0 COMMENT '楼层开通情况0没开通1已开通',
  `depart_pay` tinyint NOT NULL DEFAULT -1 COMMENT '诊间缴费-1未开通0开发1已开通',
  `need_patient_card` tinyint NOT NULL DEFAULT 0 COMMENT '就诊卡0 不需要就诊卡 1、自动生成 2、手动填写和自动开卡  3手工录入',
  `takeno_guide` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '取号指南',
  `treat_wait` tinyint NOT NULL DEFAULT 0 COMMENT '就诊等候',
  `doctor_stop` tinyint NOT NULL DEFAULT 0 COMMENT '医生停诊通知',
  `inhospital_pay` tinyint NOT NULL DEFAULT 0 COMMENT '住院缴费1开通0未',
  `report` tinyint NOT NULL DEFAULT 0 COMMENT '报告单',
  `mz_recharge` int NOT NULL DEFAULT -1 COMMENT '门诊充值 -1关闭 0待开通 1开通',
  `zy_recharge` int NOT NULL DEFAULT -1 COMMENT '住院充值 -1关闭0待开通 1开通',
  `is_guide` int NOT NULL DEFAULT -1 COMMENT '智能导诊-1关闭 0待开通 1开通',
  `is_datepart` int NOT NULL DEFAULT 0 COMMENT '分时段 0不分 1分时段不显示序号 2分时段显示序号',
  `allow_patient_type` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '允许录入的就诊人  0成人 1儿童 2成人和儿童',
  `patient_no_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '唯一数字的称号  如就诊卡 门诊号',
  `pay_limit_minute` int NOT NULL DEFAULT 30 COMMENT '待支付时间限制 默认30分钟',
  `card_reported_loss` tinyint(1) NOT NULL DEFAULT -1 COMMENT '挂失就诊卡 -1关闭 0待开通 1开通',
  `hos_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '医院类型 1公立 2专科 4疫苗 5特需',
  `baidu_map_keywords` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '百度地图关键字',
  `appoint_sign` int NOT NULL DEFAULT 0 COMMENT '预约记录签到',
  `accompany_status` tinyint NOT NULL DEFAULT 0 COMMENT '陪诊0未开通1已开通',
  `stop_start_time` int NOT NULL DEFAULT 0 COMMENT '业务停止日期时间',
  `stop_end_time` int NOT NULL DEFAULT 0 COMMENT '业务停止日期时间',
  `appoint_base_num` int NOT NULL DEFAULT 0 COMMENT '医院预约基数',
  `list_sort` int NOT NULL DEFAULT 99 COMMENT '列表排序',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注说明',
  `env_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医院环境展示',
  `check_idcard` int NOT NULL DEFAULT -1 COMMENT '用户需要实名制验证',
  `check_patient` int NOT NULL DEFAULT -1 COMMENT '就诊需要手机号验证',
  `is_vaccine` tinyint(1) NOT NULL DEFAULT -1 COMMENT '是否为疫苗预约医院 -1 未开通 1 测试  2 开通 ',
  `vaccine_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pat_addr_must` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0不需要1需要',
  `need_insurance` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1支持0不支持',
  `hos_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hos_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `support_passport` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否支持护照',
  `free_banner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '义诊banner',
  `is_inner_system` tinyint NOT NULL DEFAULT 0 COMMENT '是否使用小系统',
  `area_code` char(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区域代码',
  `allow_child_no_card` tinyint NULL DEFAULT 0 COMMENT '允许儿童无身份证进行预约',
  `allow_today_registe` tinyint NOT NULL DEFAULT 0 COMMENT '是否允许当天挂号',
  `allow_day_back` tinyint NOT NULL DEFAULT 0 COMMENT '允不允许当天退款，1允许0允许',
  `back_money_time` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退款时间点',
  `appoint_days` int NOT NULL DEFAULT 1 COMMENT '提前预约天数',
  `is_visit_address` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示就诊地址，0否1是',
  `new_crown_vaccine` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有新冠疫苗0否1是',
  `street` int NULL DEFAULT NULL COMMENT '街道',
  `back_registe_time` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '16:00' COMMENT '停止预约时间',
  `allow_system_refund` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否后台退款0否1是',
  `register_cancel` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示订单核销0否1是',
  `second_depart` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示二级科室0否1是',
  `index_hide` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否首页预约挂号隐藏0否1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `level_id`(`level_id` ASC) USING BTREE,
  INDEX `city_id`(`city_id` ASC) USING BTREE,
  INDEX `hospital_code`(`hospital_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 408 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_hospital_activity
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_activity`;
CREATE TABLE `api_hospital_activity`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院编码',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动主题',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图片',
  `introduce` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动介绍',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动报名价格',
  `max_num` int NOT NULL COMMENT '活动最大人数',
  `appoint_num` int NOT NULL DEFAULT 0 COMMENT '活动报名人数',
  `activity_date` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动日期(Y-m-d)',
  `start_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动报名开始时间',
  `end_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动结束时间',
  `link_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接地址',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院活动表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_appt_cate
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_appt_cate`;
CREATE TABLE `api_hospital_appt_cate`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室ID',
  `dep_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `first_char` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室首字母',
  `dep_intro` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室简介',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室地址',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_recomm` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `ask_dep_id` int NOT NULL DEFAULT 0 COMMENT '在线问诊关联科室ID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
  `one_dept_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室code',
  `one_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一级科室名称',
  `two_dept_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二级科室名称',
  `dep_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室弹窗提醒',
  `two_dept_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_hd`(`hos_id` ASC, `dep_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 157289 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方科室信息完善表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_branch
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_branch`;
CREATE TABLE `api_hospital_branch`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `area_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分院名称',
  `area_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地址',
  `area_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `area_id` int NOT NULL COMMENT '医院code',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `hos_id` int NOT NULL COMMENT '显示医院code',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院分院' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_case
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_case`;
CREATE TABLE `api_hospital_case`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `head_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `hos_id` int NULL DEFAULT NULL COMMENT '医院id',
  `introduce` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简介',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情',
  `type` int NULL DEFAULT NULL COMMENT '分类',
  `status` int NOT NULL DEFAULT 0 COMMENT '0禁用1正常',
  `images` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面多图',
  `examine` int NOT NULL DEFAULT 0 COMMENT '审核 0待审核 1审核成功 2审核失败 ',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `examine_time` int NULL DEFAULT NULL COMMENT '审核时间',
  `examine_user` int NULL DEFAULT NULL COMMENT '审核人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院案例' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_case_type
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_case_type`;
CREATE TABLE `api_hospital_case_type`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sort` int NULL DEFAULT NULL,
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `hos_id` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0 COMMENT '状态0禁用1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_category
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_category`;
CREATE TABLE `api_hospital_category`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院类名',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `cate_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1医院列表用 2 疫苗列表用',
  `tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角标',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_level
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_level`;
CREATE TABLE `api_hospital_level`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `level_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院等级表',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间内',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院等级表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_menu
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_menu`;
CREATE TABLE `api_hospital_menu`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` int NOT NULL COMMENT '医院code',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单名',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `class` int NOT NULL DEFAULT 0 COMMENT '分类',
  `type` int NOT NULL DEFAULT 0 COMMENT '类型 0路径1链接2小程序3去院导航',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `appid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序appid',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0禁用1开发2启用',
  `appid_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '小程序显示状态0禁用1启用',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `is_pop_window` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否弹窗 0否 1是',
  `pop_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '弹窗内容',
  `is_pop_next` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否下一步 0否1是',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 239 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院菜单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_menu_class
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_menu_class`;
CREATE TABLE `api_hospital_menu_class`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL COMMENT '状态0禁用1开发2启用',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_menu_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_menu_rule`;
CREATE TABLE `api_hospital_menu_rule`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `route` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路径',
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `param_hos_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院/体检 赋值参数',
  `param_extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '额外参数',
  `is_check` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否跳体检0否1是',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0禁用1启用',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `type` int NOT NULL DEFAULT 0 COMMENT '类型 0默认路径 3导航',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_hospital_type
-- ----------------------------
DROP TABLE IF EXISTS `api_hospital_type`;
CREATE TABLE `api_hospital_type`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院类型名称',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_id_generation
-- ----------------------------
DROP TABLE IF EXISTS `api_id_generation`;
CREATE TABLE `api_id_generation`  (
  `parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `sub_id` int NOT NULL,
  PRIMARY KEY (`parent_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_images_info
-- ----------------------------
DROP TABLE IF EXISTS `api_images_info`;
CREATE TABLE `api_images_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件目录',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说明',
  `height` int NOT NULL DEFAULT 0 COMMENT '高度(为0时不限制)',
  `width` int NOT NULL DEFAULT 0 COMMENT '长度(为0时不限制)',
  `size` int NOT NULL DEFAULT 0 COMMENT '最大(为0时不限制) 单位kb',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `path`(`path` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图片控制' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_income
-- ----------------------------
DROP TABLE IF EXISTS `api_income`;
CREATE TABLE `api_income`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `doctor_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT ' 用户ID',
  `follow_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注类型1：关注0：取消关注',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `imp_time` int NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_userid`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32784 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医师关注表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_influenza_create
-- ----------------------------
DROP TABLE IF EXISTS `api_influenza_create`;
CREATE TABLE `api_influenza_create`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `bill_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开单id',
  `hos_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院code',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '滇医通就诊人id',
  `jz_card` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊卡',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_influenza_create_create_time_IDX`(`create_time` ASC) USING BTREE,
  INDEX `api_influenza_create_hos_id_IDX`(`hos_id` ASC) USING BTREE,
  INDEX `api_influenza_create_bill_id_IDX`(`bill_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3421 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_inner_depart_bak
-- ----------------------------
DROP TABLE IF EXISTS `api_inner_depart_bak`;
CREATE TABLE `api_inner_depart_bak`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `depart_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属医院id',
  `depart_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '科室类型 1普通 2热门 3 特色 ',
  `depart_py` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室拼音',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室地址',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 210 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院科室表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_inner_departs
-- ----------------------------
DROP TABLE IF EXISTS `api_inner_departs`;
CREATE TABLE `api_inner_departs`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `depart_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属医院id',
  `depart_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '科室类型 1普通 2热门 3 特色 ',
  `depart_py` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室拼音',
  `dep_addr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室地址',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态1启用0开发-1禁用',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `reservation_type` int NOT NULL DEFAULT 1 COMMENT '预约类型 1: 普通预约 2:登记',
  `pid` int NOT NULL DEFAULT 0 COMMENT '上级id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `hospital_id_IDX`(`hospital_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1427 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院科室表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_inner_doctor_check
-- ----------------------------
DROP TABLE IF EXISTS `api_inner_doctor_check`;
CREATE TABLE `api_inner_doctor_check`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hospital_id` int NULL DEFAULT NULL COMMENT '医院id',
  `hos_id` int NULL DEFAULT NULL COMMENT '医院code',
  `doctor_id` int NOT NULL COMMENT '医生id',
  `is_age` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否限制年龄0否1是',
  `age_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '限制年龄',
  `min_age` int NULL DEFAULT NULL COMMENT '限制最低年龄',
  `max_age` int NULL DEFAULT NULL COMMENT '限制最大年龄',
  `is_sex` tinyint(1) NOT NULL COMMENT '是否限制性别0否1是',
  `sex` int NOT NULL DEFAULT 0 COMMENT '性别1男2女',
  `is_birthday` int NOT NULL COMMENT '是否限制出生日0否1是',
  `date_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '限制生日',
  `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `is_area` int NOT NULL COMMENT '开启允许挂号地区0否1是',
  `area` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '允许挂号地区',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `is_not_area` int NOT NULL COMMENT '开启不允许挂号地区0否1是',
  `not_area` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '不允许挂号地区',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生身份证挂号限制' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_inner_doctors
-- ----------------------------
DROP TABLE IF EXISTS `api_inner_doctors`;
CREATE TABLE `api_inner_doctors`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属医院',
  `depart_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属部门',
  `doctor_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生真实姓名',
  `doctor_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '医生简介',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间 ',
  `doctor_avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生头像',
  `doctor_good` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '擅长，一句话',
  `doctor_position` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '职称，教授',
  `doc_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `tag_id` int NOT NULL DEFAULT 0 COMMENT '标签组ID',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '标签组内排序号',
  `is_show` int NOT NULL DEFAULT 1 COMMENT '医生主页是否显示 1 显示  -1 不显示',
  `doc_sort` int NOT NULL DEFAULT 255 COMMENT '医生主页排序',
  `doctor_duty_info` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出诊信息',
  `reservation_rules` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '预约规则',
  `is_multidisciplinary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否多学科医生0否1是',
  `offline_payment_tips` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '线下缴费提醒',
  `notice` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '订单通知人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `depart_id`(`depart_id` ASC) USING BTREE,
  INDEX `api_inner_doctors_hospital_id_IDX`(`hospital_id` ASC, `depart_id` ASC) USING BTREE,
  INDEX `api_inner_doctors_doctor_name_IDX`(`doctor_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5849 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_inspect_register
-- ----------------------------
DROP TABLE IF EXISTS `api_inspect_register`;
CREATE TABLE `api_inspect_register`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `group_id` int NOT NULL DEFAULT 0 COMMENT '团队di',
  `patient_id` int NOT NULL COMMENT '就诊人id',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `hos_id` int NULL DEFAULT NULL COMMENT '医院code',
  `heart_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '心脏彩超图',
  `ecg_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '心电图',
  `ct_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ct图',
  `report_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查报告',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 98 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查登记' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_jobs
-- ----------------------------
DROP TABLE IF EXISTS `api_jobs`;
CREATE TABLE `api_jobs`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED NULL DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_key_info_app
-- ----------------------------
DROP TABLE IF EXISTS `api_key_info_app`;
CREATE TABLE `api_key_info_app`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `keyInfo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43936 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_keyword_reply
-- ----------------------------
DROP TABLE IF EXISTS `api_keyword_reply`;
CREATE TABLE `api_keyword_reply`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `keyword` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键词',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回访内容',
  `is_same` tinyint NOT NULL DEFAULT 1 COMMENT '是否完全一样',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:1启用,0:禁用,-1:删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '关键词回复' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_links
-- ----------------------------
DROP TABLE IF EXISTS `api_links`;
CREATE TABLE `api_links`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'logo',
  `linker` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人说明',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1 开启 0 关闭',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序 ',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '友情 链接' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_live
-- ----------------------------
DROP TABLE IF EXISTS `api_live`;
CREATE TABLE `api_live`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `cover` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '封面图地址',
  `homepage` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主讲人首页',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '详细介绍',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者id',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人id',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_livemovie
-- ----------------------------
DROP TABLE IF EXISTS `api_livemovie`;
CREATE TABLE `api_livemovie`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `cover` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '封面图片地址',
  `view_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '观看地址',
  `clicks` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者id',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者id',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `live_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频专栏id',
  `live_date` date NOT NULL COMMENT '直播日期',
  `live_time` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '直播时间段',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '视频价格',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_liveother
-- ----------------------------
DROP TABLE IF EXISTS `api_liveother`;
CREATE TABLE `api_liveother`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `file_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '文件地址',
  `clicks` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者id',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者id',
  `live_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频专栏id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_login_log
-- ----------------------------
DROP TABLE IF EXISTS `api_login_log`;
CREATE TABLE `api_login_log`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录ip',
  `create_time` int NULL DEFAULT NULL COMMENT '登录时间',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录提示',
  `type` int NULL DEFAULT 0 COMMENT '登录人员分类 0默认后台用户1医师登录用户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 348659 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '登录日志' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_lumbar_surgery
-- ----------------------------
DROP TABLE IF EXISTS `api_lumbar_surgery`;
CREATE TABLE `api_lumbar_surgery`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `patient_id` int NOT NULL COMMENT '就诊人id',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `hos_id` int NULL DEFAULT NULL COMMENT '医院code',
  `MRI_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'MRI图',
  `vertebra_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '腰椎图心电图',
  `ct_images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ct图',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `day1_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '今日血糖数据',
  `day2_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昨日数据',
  `day3_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '前日数据',
  `history_of_diabetes` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '糖尿病史0否1是',
  `history_of_hypertension` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '高血压史 0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 91 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '腰椎手术预约' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_dp_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_dp_relation`;
CREATE TABLE `api_manage_dp_relation`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户IDwechat_id',
  `wechat_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'wx id',
  `wechat_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者头像',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '缓存患者名',
  `doc_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问诊组id',
  `doc_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生ID',
  `doc_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师头像',
  `doc_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师名',
  `doc_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `doc_hospital_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师医院',
  `doc_depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师科室',
  `doc_unique_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '唯一id',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态,-3:删除,-2:屏蔽,-1:未通过,0:申请待审核,1:审核通过',
  `from_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源分组1,2报到3图文4电话5服务包',
  `is_important` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否重点关注',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '申请时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `accept_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请通过时间',
  `log_show` tinyint NOT NULL DEFAULT 0 COMMENT '聊天页是否显示',
  `has_nums` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '剩余次数',
  `over_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '停止聊天时间戳',
  `is_new` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为新患者',
  `other_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '其他信息',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53460 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医患关系表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_dp_report
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_dp_report`;
CREATE TABLE `api_manage_dp_report`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `relation_id` int UNSIGNED NOT NULL COMMENT '关系主键',
  `last_jz_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后就诊时间',
  `disease` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '什么病',
  `type` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '就诊类型,1:门诊患者,2:住院患者',
  `treat_plan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '诊疗建议',
  `in_hospital` int NOT NULL DEFAULT 0 COMMENT '是否在院:-1出院,1在院',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1990 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者报到信息' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_dp_set
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_dp_set`;
CREATE TABLE `api_manage_dp_set`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `doc_user_id` int NOT NULL DEFAULT 0 COMMENT '医生ID',
  `check_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '审核方式:1:手动,2:自动',
  `report_ask_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '报到后赠送问诊次数',
  `visit_ask_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '随访赠送问诊次数',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doc_user_id_index`(`doc_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1137 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者报到医生端设置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_dp_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_dp_tag`;
CREATE TABLE `api_manage_dp_tag`  (
  `relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关系id',
  `dtag_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '标签id',
  `doc_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师id',
  INDEX `relation_id_index`(`relation_id` ASC) USING BTREE,
  INDEX `dtag_id_index`(`dtag_id` ASC) USING BTREE,
  INDEX `doc_user_id_index`(`doc_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_dtag
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_dtag`;
CREATE TABLE `api_manage_dtag`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态1启用-1禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `doc_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师id 0公用',
  `nums` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '分组有多少人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 99 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_manage_log
-- ----------------------------
DROP TABLE IF EXISTS `api_manage_log`;
CREATE TABLE `api_manage_log`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '会话id',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '全局用户的唯一id',
  `from_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送人',
  `group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '团队 group id',
  `to_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '接收人',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `content_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未知 1text 2img 3voice',
  `message_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0随访1在线问诊主问题2追问3回复，4-加班门诊订单',
  `is_show` tinyint NOT NULL DEFAULT 1 COMMENT '-1删除0撤回1显示',
  `patient_read` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者读取',
  `doc_read` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未读1已读',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最好别用了',
  `is_doc_say` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为医师发言',
  `voice_time_length` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '只有语音有时长',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26943 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者管理聊天表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_max_order_dep
-- ----------------------------
DROP TABLE IF EXISTS `api_max_order_dep`;
CREATE TABLE `api_max_order_dep`  (
  `id` int(10) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hos_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `dep_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `dep_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `num` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_menu
-- ----------------------------
DROP TABLE IF EXISTS `api_menu`;
CREATE TABLE `api_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `parent_id` bigint NOT NULL COMMENT '父菜单ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单别名',
  `level` tinyint NOT NULL DEFAULT 0 COMMENT '菜单层级',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单描述',
  `icon` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单图标',
  `is_recommend` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `jump_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转URL',
  `sort` tinyint NOT NULL DEFAULT 127 COMMENT '排序',
  `version` tinyint NOT NULL COMMENT '版本',
  `extra_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平级菜单标签',
  `need_num` tinyint NOT NULL DEFAULT 0 COMMENT '需要计数',
  `status` int NOT NULL COMMENT '状态0禁用1正常-1开发中',
  `is_applet` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否小程序显示0否1是',
  `top_subscript` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '右上标',
  `is_account` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公众号显示0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_menu_copy1
-- ----------------------------
DROP TABLE IF EXISTS `api_menu_copy1`;
CREATE TABLE `api_menu_copy1`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `parent_id` bigint NOT NULL COMMENT '父菜单ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单别名',
  `level` tinyint NOT NULL DEFAULT 0 COMMENT '菜单层级',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单描述',
  `icon` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单图标',
  `is_recommend` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `jump_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转URL',
  `sort` tinyint NOT NULL DEFAULT 127 COMMENT '排序',
  `version` tinyint NOT NULL COMMENT '版本',
  `extra_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平级菜单标签',
  `need_num` tinyint NOT NULL DEFAULT 0 COMMENT '需要计数',
  `status` int NOT NULL COMMENT '状态0禁用1正常-1开发中',
  `is_applet` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否小程序显示0否1是',
  `top_subscript` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '右上标',
  `is_account` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公众号显示0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_metaverse
-- ----------------------------
DROP TABLE IF EXISTS `api_metaverse`;
CREATE TABLE `api_metaverse`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `speaker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主讲人',
  `title_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称',
  `start_time` int NOT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '结束时间',
  `depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室',
  `depart_id` int NOT NULL COMMENT '科室id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `video_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频链接',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶 0否1是',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签',
  `remarke` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `hos_id` int NOT NULL COMMENT '医院code',
  `video_length` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时长',
  `video_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '大小',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类别',
  `show_time` int NOT NULL COMMENT '显示时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 222 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页多媒体' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_metaverse_class
-- ----------------------------
DROP TABLE IF EXISTS `api_metaverse_class`;
CREATE TABLE `api_metaverse_class`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `speaker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主讲人',
  `title_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称',
  `start_time` int NOT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '结束时间',
  `depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室',
  `depart_id` int NOT NULL COMMENT '科室id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `video_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频链接',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶 0否1是',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签',
  `remarke` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `hos_id` int NOT NULL COMMENT '医院code',
  `video_length` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时长',
  `video_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '大小',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类别分类 1常见疾病 2 专科专病',
  `is_recommend` int NOT NULL DEFAULT 0 COMMENT '推荐学习 0否 1是',
  `label_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `applet_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序appid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 206 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页语音' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_metaverse_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_metaverse_rule`;
CREATE TABLE `api_metaverse_rule`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0禁用 1正常',
  `start_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开始时间',
  `end_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间',
  `week` int NOT NULL COMMENT '周天',
  `metaverse_id` int NOT NULL COMMENT '头条id',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多媒体规则' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_module_visit_num
-- ----------------------------
DROP TABLE IF EXISTS `api_module_visit_num`;
CREATE TABLE `api_module_visit_num`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `num` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '功能模块访问次数统计' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_multidisciplinary
-- ----------------------------
DROP TABLE IF EXISTS `api_multidisciplinary`;
CREATE TABLE `api_multidisciplinary`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学科名',
  `hos_code_show` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '显示医院code',
  `hos_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊医院code',
  `start_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会诊时间',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `doctor_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 0禁用1启用',
  `depart_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 255 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多学科会诊' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_multidisciplinary_hospital
-- ----------------------------
DROP TABLE IF EXISTS `api_multidisciplinary_hospital`;
CREATE TABLE `api_multidisciplinary_hospital`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院code',
  `status` int NOT NULL DEFAULT 0 COMMENT '-1禁用0开发1启用',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  `sort` int NOT NULL DEFAULT 0,
  `hospital_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多学科医院' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_mz_pay_list
-- ----------------------------
DROP TABLE IF EXISTS `api_mz_pay_list`;
CREATE TABLE `api_mz_pay_list`  (
  `order_id` int NOT NULL COMMENT '门诊订单ID',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '缴费项目名称',
  `num` int NOT NULL DEFAULT 0 COMMENT '数量',
  `price` decimal(10, 2) NOT NULL COMMENT '费用金额_总价',
  INDEX `order_id_index`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门诊缴费清单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_mz_pay_order
-- ----------------------------
DROP TABLE IF EXISTS `api_mz_pay_order`;
CREATE TABLE `api_mz_pay_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `hos_id` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院id',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '患者主键',
  `jz_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '患者就诊卡ID',
  `patient_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `bill_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '单据号',
  `bill_time` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开单时间',
  `dep_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开单科室',
  `doc_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开单医生',
  `total_price` decimal(10, 2) NOT NULL COMMENT '费用金额',
  `bill_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处方类型',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '更新时间',
  `res_msg` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '调用his支付接口返回',
  `status` int NOT NULL DEFAULT 0 COMMENT '订单状态0:待支付,1:支付成功,-1:微信支付成功his支付失败,-2:已退款',
  `queue_sn` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '序号',
  `jz_address` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '就诊地址',
  `refund_time` int NULL DEFAULT 0 COMMENT '退款时间',
  `his_order_no` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'his订单号',
  `handle_times` int NULL DEFAULT 0 COMMENT '操作次数',
  `has_p` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否打印0没有1有',
  `e_invoice_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电子发票信息 机构代码|||txhash',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_order_no`(`order_no` ASC) USING BTREE,
  INDEX `index_status`(`status` ASC) USING BTREE,
  INDEX `index_bill_id`(`hos_id` ASC, `jz_card` ASC, `bill_id` ASC) USING BTREE,
  INDEX `api_mz_pay_order_user_id_IDX`(`user_id` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_createtime_patientname`(`create_time` ASC, `patient_name` ASC) USING BTREE,
  INDEX `idx_patientname_hosid`(`patient_name` ASC, `hos_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8565403 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门诊支付订单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_mz_pay_record
-- ----------------------------
DROP TABLE IF EXISTS `api_mz_pay_record`;
CREATE TABLE `api_mz_pay_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL COMMENT '门诊缴费订单表ID',
  `bill_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '单号/处方号',
  `hos_id` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院id',
  `dep_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开单科室',
  `doc_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开单医生',
  `jz_dep` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '执行科室/就诊科室',
  `bill_time` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开单时间',
  `total_price` decimal(10, 2) NOT NULL COMMENT '费用金额',
  `create_time` int NOT NULL COMMENT '创建时间',
  `res_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '调用his支付接口返回',
  `his_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'his订单号',
  `e_invoice_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子发票信息 机构代码|||txhash',
  `take_drug_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '取药号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_order_id`(`order_id` ASC) USING BTREE,
  INDEX `i_create_time`(`create_time` ASC) USING BTREE,
  INDEX `api_mz_pay_record_bill_id_IDX`(`bill_id` ASC) USING BTREE,
  INDEX `api_mz_pay_record_hos_id_IDX`(`hos_id` ASC, `bill_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10655893 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门诊缴费记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_mz_pay_refund
-- ----------------------------
DROP TABLE IF EXISTS `api_mz_pay_refund`;
CREATE TABLE `api_mz_pay_refund`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `record_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'mz_pay_record',
  `order_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'mz_pay_order',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付订单号',
  `refund_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款订单号',
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `refund_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `bill_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '处方号id',
  `jz_card` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊卡',
  `hos_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院id',
  `patient_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者姓名',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易编码',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `op_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款员',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '滇医通user_info',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id_dx`(`order_id` ASC) USING BTREE,
  INDEX `order_no_dx`(`order_no` ASC) USING BTREE,
  INDEX `refund_order_dx`(`refund_order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24509 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_mz_refund_callhis
-- ----------------------------
DROP TABLE IF EXISTS `api_mz_refund_callhis`;
CREATE TABLE `api_mz_refund_callhis`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `yuanneitfbh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '院内退费单号',
  `order_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1挂号2门诊',
  `refund_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '退费金额',
  `call_his` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未回调his,1已回调his',
  `jz_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊卡号',
  `refund_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退费单号',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付是的微信单号',
  `refund_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '退费时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_time` int UNSIGNED NOT NULL COMMENT '创建时间',
  `patient_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者名字',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退费信息请求his' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_operation_info
-- ----------------------------
DROP TABLE IF EXISTS `api_operation_info`;
CREATE TABLE `api_operation_info`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `op_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作信息',
  `op_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `op_module` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未定义,1问诊',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4268 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_pages
-- ----------------------------
DROP TABLE IF EXISTS `api_pages`;
CREATE TABLE `api_pages`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父级',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简介',
  `keyword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关键字',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态',
  `sort` int NOT NULL COMMENT '排序',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '单页面' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_passport
-- ----------------------------
DROP TABLE IF EXISTS `api_passport`;
CREATE TABLE `api_passport`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '卡号',
  `create_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `card_no`(`card_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 852 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '护照表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_patient
-- ----------------------------
DROP TABLE IF EXISTS `api_patient`;
CREATE TABLE `api_patient`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NULL DEFAULT NULL COMMENT '用户id,对应user_info中的ID',
  `hzid` int NULL DEFAULT 0 COMMENT '患者ID',
  `health_card` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电子健康卡',
  `patient_relation` int NOT NULL DEFAULT 0 COMMENT '和本人的关系 1本人 2父母 3子女 4朋友  0其他 5婴儿',
  `patient_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人身份证',
  `patient_sex` int NOT NULL DEFAULT 1 COMMENT '就诊人性别 1 男 2女',
  `patient_age` int NOT NULL COMMENT '就诊人年龄',
  `patient_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人手机号',
  `patient_birthday` date NOT NULL COMMENT '出生日期',
  `use_insure_card` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未知1市医保2自费3省医保4农合5异地医保',
  `insure_card_no` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医保卡号',
  `patient_sfy_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '市妇幼就诊卡',
  `patient_birth_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出生证号',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认就诊人',
  `share_status` tinyint NOT NULL DEFAULT 0 COMMENT '共享状态',
  `add_time` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '添加时间',
  `update_time` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `guarantee_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '监护人姓名',
  `guarantee_id_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '监护人身份证号',
  `guarantee_relation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '监护人关系',
  `patient_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人地址',
  `card_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1身份证2护照',
  `is_check` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否手机号验证(0:未验证,1:已验证)',
  `occupations_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称value',
  `occupations_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职业code',
  `nation_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职业名',
  `nation_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '民族code',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `wx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `api_patient_patient_phone_IDX`(`patient_phone` ASC) USING BTREE,
  INDEX `api_patient_patient_card_IDX`(`patient_card` ASC) USING BTREE,
  INDEX `api_patient_patient_name_IDX`(`patient_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100156952 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信用户就诊人表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_patient_card
-- ----------------------------
DROP TABLE IF EXISTS `api_patient_card`;
CREATE TABLE `api_patient_card`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `jz_card` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `patient_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '患者编号/患者索引/省中真实患者就诊卡号',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patientid_hosid_status`(`patient_id` ASC, `hos_id` ASC, `status` ASC) USING BTREE,
  INDEX `ix_jzc_hos`(`jz_card` ASC, `hos_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10227446 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '就诊卡' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_patient_card_0403bak
-- ----------------------------
DROP TABLE IF EXISTS `api_patient_card_0403bak`;
CREATE TABLE `api_patient_card_0403bak`  (
  `id` int NOT NULL DEFAULT 0,
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `jz_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NULL DEFAULT 1
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_patient_card_0427bak
-- ----------------------------
DROP TABLE IF EXISTS `api_patient_card_0427bak`;
CREATE TABLE `api_patient_card_0427bak`  (
  `id` int NOT NULL DEFAULT 0,
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `jz_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NULL DEFAULT 1
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_patient_card_190313
-- ----------------------------
DROP TABLE IF EXISTS `api_patient_card_190313`;
CREATE TABLE `api_patient_card_190313`  (
  `id` int NOT NULL DEFAULT 0,
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `jz_card` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NULL DEFAULT 1
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_patient_card_190313del
-- ----------------------------
DROP TABLE IF EXISTS `api_patient_card_190313del`;
CREATE TABLE `api_patient_card_190313del`  (
  `id` int NOT NULL DEFAULT 0,
  `patient_id` int NOT NULL COMMENT '就诊人ID',
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院ID',
  `jz_card` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊卡',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `status` int NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_pay_order
-- ----------------------------
DROP TABLE IF EXISTS `api_pay_order`;
CREATE TABLE `api_pay_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mch_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户ID',
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户标识',
  `sub_mch_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字商户ID',
  `total_price` decimal(10, 2) NOT NULL COMMENT '总价',
  `real_pay_money` decimal(10, 2) NOT NULL COMMENT '实际付款',
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户订单号',
  `transaction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付订单号',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `pay_time` int NOT NULL COMMENT '支付时间',
  `update_time` int NULL DEFAULT NULL COMMENT '更新时间',
  `status` int NOT NULL COMMENT '状态',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型0挂号1门诊2门诊充值3住院充值4问诊,6-体检缴费,7-收费进群直播,8-语音包9-就诊顾问10-健康直通车11-直通车交通费',
  `pay_channel` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付渠道',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_transaction_id`(`transaction_id` ASC) USING BTREE,
  INDEX `order_no_index_pay`(`order_no` ASC) USING BTREE,
  INDEX `openid`(`openid` ASC) USING BTREE,
  INDEX `pay_time`(`pay_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25388569 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信支付流水表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_phone_version
-- ----------------------------
DROP TABLE IF EXISTS `api_phone_version`;
CREATE TABLE `api_phone_version`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 安卓 1苹果',
  `phone_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机型号',
  `system_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统版本',
  `app_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app版本',
  `doctor_id` int NOT NULL COMMENT '医生id',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_phone_version_doctor_id_IDX`(`doctor_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11213 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `api_qrcode`;
CREATE TABLE `api_qrcode`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `scene` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '场景模型',
  `scene_value` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '场景值',
  `code_ticket` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '二维码ticket',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型 1：临时 2：永久',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '服务器地址',
  `code_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '本地地址',
  `info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '额外数据',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `scan_num` int NULL DEFAULT 0 COMMENT '扫描次数',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` int NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_sv`(`scene_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5512 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_question_confirm
-- ----------------------------
DROP TABLE IF EXISTS `api_question_confirm`;
CREATE TABLE `api_question_confirm`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `question_id` int NOT NULL COMMENT '问题id',
  `end_confirm` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0未发起结束订单状态 1已发',
  `create_time` int NOT NULL COMMENT '发起时间',
  `user_id` int NOT NULL COMMENT '操作医生微信user_id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '48小时医生发起结束订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_question_follow
-- ----------------------------
DROP TABLE IF EXISTS `api_question_follow`;
CREATE TABLE `api_question_follow`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `follow_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注类型0：浏览1：赞2：围观',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `doctor_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队主键',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_userid_questionid_followtype`(`user_id` ASC, `question_id` ASC, `follow_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152600 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题关注表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_question_follow_copy
-- ----------------------------
DROP TABLE IF EXISTS `api_question_follow_copy`;
CREATE TABLE `api_question_follow_copy`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `follow_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注类型0：浏览1：赞2：围观',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `doctor_group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1019 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题关注表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_question_glycosuria
-- ----------------------------
DROP TABLE IF EXISTS `api_question_glycosuria`;
CREATE TABLE `api_question_glycosuria`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'question_id',
  `patient_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '就诊人姓名',
  `admission_number` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '住院号',
  `outpatient_number` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门诊号',
  `admission_time` datetime NULL DEFAULT NULL COMMENT '入院时间',
  `follow_up_time` datetime NULL DEFAULT NULL COMMENT '随访时间',
  `patient_sex` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '1男2女0未知',
  `patient_birthday` datetime NULL DEFAULT NULL COMMENT '出生日期',
  `sick_age` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '发病年龄',
  `height` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '身高',
  `weight` tinyint NOT NULL,
  `insulin_dosage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '胰岛素用量',
  `insulin_injection_number` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '注射次数',
  `insulin_injection_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注射方案',
  `mg` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'MG',
  `sd` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'SD',
  `tir` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'TIR',
  `tar` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'TAR',
  `tbr` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'tbr',
  `cv` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'CV',
  `hba1c` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'HBA1C',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '糖尿病信息收集' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_question_objection
-- ----------------------------
DROP TABLE IF EXISTS `api_question_objection`;
CREATE TABLE `api_question_objection`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `question_id` int NOT NULL COMMENT '问题id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 0未处理 1已处理',
  `who` tinyint(1) NOT NULL DEFAULT 0 COMMENT '谁确认状态 0系统 1用户 2后台',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '48小时疑问订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_questions_back
-- ----------------------------
DROP TABLE IF EXISTS `api_questions_back`;
CREATE TABLE `api_questions_back`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题表主键',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '不回答原因',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '不回答医生id',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 341630 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_questions_log
-- ----------------------------
DROP TABLE IF EXISTS `api_questions_log`;
CREATE TABLE `api_questions_log`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题ID',
  `log_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '记录类型1：回答问题2：追问（未使用）3：补充答案（未使用）4：确定完成（未使用）5：公开（未使用）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `content_type` tinyint NULL DEFAULT 1 COMMENT '内容类型，null或1-纯文本，2-图片（保留类型），3-音频（保留类型），4-患者端服务包链接，5-患者端睡眠质量测试链接',
  `imgs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `old_voice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'old',
  `voice_time_length` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '录音时长',
  `voice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '音频',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师id',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_questionid_createtime`(`question_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3597023 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题回答记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_questions_logbak
-- ----------------------------
DROP TABLE IF EXISTS `api_questions_logbak`;
CREATE TABLE `api_questions_logbak`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `question_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题ID',
  `log_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '记录类型1：回答问题2：追问3：补充答案4：确定完成5：公开',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `imgs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `voice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '音频',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18861 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题回答记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_questions_refund
-- ----------------------------
DROP TABLE IF EXISTS `api_questions_refund`;
CREATE TABLE `api_questions_refund`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `order_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付流水表的order_no',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作员id',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未退款1已退款',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0问题异常1提现异常',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2669 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '退款记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_questions_words
-- ----------------------------
DROP TABLE IF EXISTS `api_questions_words`;
CREATE TABLE `api_questions_words`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `rel_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `word` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_rel_id`(`rel_id` ASC) USING BTREE,
  INDEX `ix_word`(`word` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1694410 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPRESSED;

-- ----------------------------
-- Table structure for api_region
-- ----------------------------
DROP TABLE IF EXISTS `api_region`;
CREATE TABLE `api_region`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `city_id` int NOT NULL,
  `region_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sort` int NOT NULL DEFAULT 255,
  `status` tinyint NOT NULL DEFAULT 1,
  `update_time` int NOT NULL,
  `create_time` int NOT NULL,
  `pid` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `city_id`(`city_id` ASC) USING BTREE,
  CONSTRAINT `region_city_id` FOREIGN KEY (`city_id`) REFERENCES `api_city` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_relation_ask_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_relation_ask_depart`;
CREATE TABLE `api_relation_ask_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dep_id` int NOT NULL COMMENT '在线问诊科室id',
  `relation_depart` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的二级科室',
  `status` tinyint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '在线问诊科室关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_relation_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_relation_depart`;
CREATE TABLE `api_relation_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '医院code',
  `dep_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科室code',
  `relation_depart` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '所属哪个二级科室 （api_depart_classify）表,id字段',
  `status` tinyint NOT NULL COMMENT '1 启用 0 禁用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5944 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_resource
-- ----------------------------
DROP TABLE IF EXISTS `api_resource`;
CREATE TABLE `api_resource`  (
  `res_uni_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资源全局key',
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资源',
  `resource_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资源说明',
  `resource_pos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资源位置',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '资源创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`res_uni_id`) USING BTREE,
  UNIQUE INDEX `s_index`(`res_uni_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_resource_count
-- ----------------------------
DROP TABLE IF EXISTS `api_resource_count`;
CREATE TABLE `api_resource_count`  (
  `res_uni_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资源全局key',
  `click_num` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击量',
  `view_num` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览量',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '每小时为粒度',
  PRIMARY KEY (`res_uni_id`, `create_time`) USING BTREE,
  UNIQUE INDEX `s_index`(`res_uni_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_role
-- ----------------------------
DROP TABLE IF EXISTS `api_role`;
CREATE TABLE `api_role`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否启用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简单说明',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_role_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_role_rule`;
CREATE TABLE `api_role_rule`  (
  `role_id` int NOT NULL,
  `rule_id` int NOT NULL,
  UNIQUE INDEX `fu`(`role_id` ASC, `rule_id` ASC) USING BTREE,
  INDEX `role_rule_rule_id`(`rule_id` ASC) USING BTREE,
  CONSTRAINT `role_rule_role_id` FOREIGN KEY (`role_id`) REFERENCES `api_role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `role_rule_rule_id` FOREIGN KEY (`rule_id`) REFERENCES `api_rule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_rule`;
CREATE TABLE `api_rule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父菜单',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'url地址 c+a',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `islink` tinyint NOT NULL DEFAULT 0 COMMENT '是否菜单',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `rulename`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 875 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限&菜单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_scan_sence
-- ----------------------------
DROP TABLE IF EXISTS `api_scan_sence`;
CREATE TABLE `api_scan_sence`  (
  `sence_id` int NOT NULL AUTO_INCREMENT,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `exp_date` timestamp NULL DEFAULT NULL,
  `login_date` timestamp NULL DEFAULT NULL,
  `login_openid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`sence_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 466360 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方扫二维码登陆网页记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_search_count
-- ----------------------------
DROP TABLE IF EXISTS `api_search_count`;
CREATE TABLE `api_search_count`  (
  `keyword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '搜索关键词',
  `search_num` int NOT NULL DEFAULT 1 COMMENT '搜索次数',
  PRIMARY KEY (`keyword`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '关键词搜索统计' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_search_departs
-- ----------------------------
DROP TABLE IF EXISTS `api_search_departs`;
CREATE TABLE `api_search_departs`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `depart_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室logo',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态2开发1启用0禁用-1删除',
  `sort` int UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `pid` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级id',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `active_user` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '快速问诊活跃人数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 510 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医院科室表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_search_record
-- ----------------------------
DROP TABLE IF EXISTS `api_search_record`;
CREATE TABLE `api_search_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '搜索关键词',
  `wuser_id` int NULL DEFAULT 0 COMMENT '搜索用户ID',
  `channel` int NULL DEFAULT 0 COMMENT '搜索渠道,1:在线问诊',
  `create_time` int NULL DEFAULT NULL COMMENT '搜索时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2340847 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户搜索记录' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_selection_problem_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_selection_problem_tag`;
CREATE TABLE `api_selection_problem_tag`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'tag名称',
  `tag_sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `tag_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `display_num` int NULL DEFAULT 10,
  `status` int NOT NULL DEFAULT 1 COMMENT '1启用 -1 禁用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_selection_problem_tag_que
-- ----------------------------
DROP TABLE IF EXISTS `api_selection_problem_tag_que`;
CREATE TABLE `api_selection_problem_tag_que`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `que_id` int NOT NULL,
  `status` int NOT NULL DEFAULT 1 COMMENT '1 启用 -1 禁用',
  `doctor_group_id` int NOT NULL DEFAULT 0,
  `que_sort` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 120 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_service_category
-- ----------------------------
DROP TABLE IF EXISTS `api_service_category`;
CREATE TABLE `api_service_category`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类名',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简介',
  `keyword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词',
  `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  `status` tinyint NULL DEFAULT 0 COMMENT '状态0禁用 1启用',
  `cate_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类编码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `parent`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_service_item
-- ----------------------------
DROP TABLE IF EXISTS `api_service_item`;
CREATE TABLE `api_service_item`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `cate_id` int NULL DEFAULT NULL COMMENT '分类ID',
  `service_id` int NOT NULL COMMENT '产品ID',
  `service_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '栏目名称',
  `service_detail` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '服务详情',
  `sort` tinyint NOT NULL COMMENT '排序',
  `status` tinyint NOT NULL COMMENT '状态',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_service_product
-- ----------------------------
DROP TABLE IF EXISTS `api_service_product`;
CREATE TABLE `api_service_product`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` int NULL DEFAULT NULL COMMENT '服务分类',
  `service_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务编码（唯一）',
  `params` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '服务参数',
  `service_type` tinyint NULL DEFAULT NULL COMMENT '服务类型',
  `service_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务产品名称',
  `cover_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务封面',
  `service_intro` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务简介',
  `service_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '服务价格',
  `service_num` int NULL DEFAULT NULL COMMENT '人数',
  `sort` tinyint NULL DEFAULT NULL COMMENT '排序',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 0禁用 1启用',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `service_tel` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_set_agreement
-- ----------------------------
DROP TABLE IF EXISTS `api_set_agreement`;
CREATE TABLE `api_set_agreement`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `agree_id` int NOT NULL COMMENT '协议id',
  `user_id` int NOT NULL COMMENT '用户di',
  `user_type` int NOT NULL COMMENT '用户类型 1医生',
  `create_time` int NOT NULL COMMENT '添加时间',
  `enclosure` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3486 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生签订协议' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_special
-- ----------------------------
DROP TABLE IF EXISTS `api_special`;
CREATE TABLE `api_special`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'logo图标',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专病科室表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_special_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_special_depart`;
CREATE TABLE `api_special_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `special_id` int NOT NULL,
  `hos_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dep_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `recommend` int NOT NULL DEFAULT 0 COMMENT '专科科室推荐权重',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 229 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专病医院科室关联' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialhos_symptom
-- ----------------------------
DROP TABLE IF EXISTS `api_specialhos_symptom`;
CREATE TABLE `api_specialhos_symptom`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `syskey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病id',
  `disease_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '疾病名称',
  `sub_dep_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联子科室id',
  `sub_dep_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专病专诊中的疾病分类' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_class
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_class`;
CREATE TABLE `api_specialty_class`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL COMMENT '状态0禁用1开发2启用',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  `type` int NOT NULL DEFAULT 1 COMMENT '分类',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专科分类' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_depart`;
CREATE TABLE `api_specialty_depart`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `speaker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主讲人',
  `start_time` int NOT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '结束时间',
  `depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室',
  `depart_id` int NOT NULL DEFAULT 0 COMMENT '科室id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `hos_id` int NOT NULL COMMENT '医院code',
  `type` int NOT NULL COMMENT '分类',
  `title_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 221 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '科室专区' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_home
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_home`;
CREATE TABLE `api_specialty_home`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专区名',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL COMMENT '状态-2删除-1禁用0开发中1启用',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '修改时间',
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页专区' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_home_rule
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_home_rule`;
CREATE TABLE `api_specialty_home_rule`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `start_time` int NOT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL COMMENT '状态0禁用1启用',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  `home_names` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专区ids',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页专区规则' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_home_sort
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_home_sort`;
CREATE TABLE `api_specialty_home_sort`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `rule_id` int NOT NULL COMMENT '规则id',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `home_id` int NOT NULL COMMENT '专区id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页专区规则排序' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_night
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_night`;
CREATE TABLE `api_specialty_night`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` int NOT NULL COMMENT '医院code',
  `type` int NOT NULL COMMENT '分类',
  `depart_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名',
  `depart_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室code',
  `depart_id` int NOT NULL DEFAULT 0 COMMENT '科室id',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态0正常1禁用',
  `show_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开诊时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '夜间门诊' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_specialty_weekend
-- ----------------------------
DROP TABLE IF EXISTS `api_specialty_weekend`;
CREATE TABLE `api_specialty_weekend`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `hos_id` int NOT NULL COMMENT '医院code',
  `type` int NOT NULL COMMENT '分类',
  `depart_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '科室ids',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态0正常1禁用',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int NULL DEFAULT NULL,
  `update_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '周末门诊' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_stop_schedule
-- ----------------------------
DROP TABLE IF EXISTS `api_stop_schedule`;
CREATE TABLE `api_stop_schedule`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `schedule_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '停诊信息',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1:挂号',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未处理1已处理',
  `hos_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2493 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_stop_work
-- ----------------------------
DROP TABLE IF EXISTS `api_stop_work`;
CREATE TABLE `api_stop_work`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `schedule_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '排班id',
  `appoint_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '预约主键',
  `hos_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院id',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '处理状态',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51600 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_symptom
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom`;
CREATE TABLE `api_symptom`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `syskey` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `pinyin` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `body_id` int NULL DEFAULT 0,
  `sex` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别',
  `symname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `summary` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `related_dept` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `sort` int NULL DEFAULT 0,
  `collect_disease_time` int NOT NULL DEFAULT 0,
  `has_reset` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `syskey`(`syskey` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 777 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_depart
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_depart`;
CREATE TABLE `api_symptom_depart`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `pinyin` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_depart_askdepart_recommend
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_depart_askdepart_recommend`;
CREATE TABLE `api_symptom_depart_askdepart_recommend`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `symptom_depart_id` int NULL DEFAULT NULL COMMENT '症状科室',
  `symptom_depart_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ask_depart_id` int NULL DEFAULT 0,
  `ask_depart_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `sort` int NULL DEFAULT 255 COMMENT '排序升序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 259 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_depart_recommend
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_depart_recommend`;
CREATE TABLE `api_symptom_depart_recommend`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `symptom_depart_id` int NULL DEFAULT NULL COMMENT '症状科室',
  `symptom_depart_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hospital_type` tinyint(1) NULL DEFAULT NULL COMMENT '1:his,2:clinic,3:inner',
  `hospital_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hospital_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `depart_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `depart_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hospital_longlat` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hospital_address` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sort` int NULL DEFAULT 255 COMMENT '排序升序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_disease
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_disease`;
CREATE TABLE `api_symptom_disease`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `sex` int NOT NULL DEFAULT 0 COMMENT '性别',
  `age` int NOT NULL DEFAULT 0 COMMENT '年龄',
  `symptom_id` int NOT NULL DEFAULT 0,
  `symptom_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '症状名称',
  `disease_id` int NOT NULL DEFAULT 0,
  `disease_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '疾病名称',
  `score` double(19, 18) NOT NULL DEFAULT 0.000000000000000000,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_symptom_disease_disease_id_IDX`(`disease_id` ASC) USING BTREE,
  INDEX `api_symptom_disease_symptom_id_IDX`(`symptom_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152499 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '病症疾病对应表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_search
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_search`;
CREATE TABLE `api_symptom_search`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `body_id` int NOT NULL,
  `sex` int NOT NULL,
  `age` int NOT NULL,
  `syskey` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `symname` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '症状名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3144 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_symptom_symptom
-- ----------------------------
DROP TABLE IF EXISTS `api_symptom_symptom`;
CREATE TABLE `api_symptom_symptom`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `sex` tinyint(1) NULL DEFAULT 0 COMMENT '性别',
  `age` int NULL DEFAULT 0 COMMENT '年龄',
  `symid1` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `symid2` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `has_reset` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37545 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_tag`;
CREATE TABLE `api_tag`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态-2禁用-1开发0维护1启用',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_third_order
-- ----------------------------
DROP TABLE IF EXISTS `api_third_order`;
CREATE TABLE `api_third_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `total_price` double NOT NULL,
  `redirect_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` int NULL DEFAULT NULL COMMENT '1-已付款 2-已完成（到货）5-已退款',
  `create_time` datetime NOT NULL,
  `from` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dyt_open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户关注滇医通后的微信open id',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6484 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_third_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `api_third_order_detail`;
CREATE TABLE `api_third_order_detail`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_price` double NOT NULL COMMENT '商品单价',
  `product_num` int NOT NULL COMMENT '商品数量',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9234 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方订单明细' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_third_part_account
-- ----------------------------
DROP TABLE IF EXISTS `api_third_part_account`;
CREATE TABLE `api_third_part_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `channel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道名',
  `identity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道验证方式',
  `identity_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '验证id',
  `open_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信open_id',
  `user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `wechat_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信ID',
  `is_new_user` smallint NOT NULL COMMENT '是否是新用户',
  `ordered` smallint NOT NULL COMMENT '是否下过单',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 191 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方账号绑定' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_timer_date
-- ----------------------------
DROP TABLE IF EXISTS `api_timer_date`;
CREATE TABLE `api_timer_date`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0失败1成功',
  `info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '生成工作安排的说明',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `doctor_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医生表主键',
  `depart_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '科室表主键',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院表主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 390820 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_topic
-- ----------------------------
DROP TABLE IF EXISTS `api_topic`;
CREATE TABLE `api_topic`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院code',
  `wechat_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'wechat_id',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `topic_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '话题标题',
  `sort` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序越大越靠前',
  `view_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `reply_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0待审核1显示-1禁用',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `show_reply_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '允许显示的回复id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `hos_id`(`hos_id` ASC) USING BTREE,
  INDEX `show_reply_id`(`show_reply_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68001 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_topic_reply
-- ----------------------------
DROP TABLE IF EXISTS `api_topic_reply`;
CREATE TABLE `api_topic_reply`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `wechat_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'wechat_id',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `useful_num` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `usefulers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '有用人的wechatid',
  `reply_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复的信息',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '-1禁用0待审1过审',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `topic_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '话题表主键',
  `update_id` int NOT NULL COMMENT '修改者ID',
  `checker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人',
  `is_nick` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0不匿名1匿名',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tid`(`topic_id` ASC) USING BTREE,
  CONSTRAINT `tid` FOREIGN KEY (`topic_id`) REFERENCES `api_topic` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7720 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_topic_tip
-- ----------------------------
DROP TABLE IF EXISTS `api_topic_tip`;
CREATE TABLE `api_topic_tip`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `wechat_id` int NOT NULL COMMENT '微信用户ID',
  `topic_id` int NULL DEFAULT NULL COMMENT '话题ID',
  `tip_type` tinyint(1) NOT NULL COMMENT '举报类型',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '举报内容',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态:0待处理,1已处理',
  `create_time` int NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '话题举报' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_trade_income_rate
-- ----------------------------
DROP TABLE IF EXISTS `api_trade_income_rate`;
CREATE TABLE `api_trade_income_rate`  (
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `rate` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计提比例',
  `dyt_fee` decimal(10, 0) NULL DEFAULT 0 COMMENT '滇医通手续费承担比例',
  `other_fee` decimal(10, 0) NULL DEFAULT 0 COMMENT '他方承担手续费比例'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '滇医通收费提成比例' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user
-- ----------------------------
DROP TABLE IF EXISTS `api_user`;
CREATE TABLE `api_user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户邮件地址',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户密码',
  `role_id` int NOT NULL COMMENT '用户角色',
  `status` tinyint NOT NULL COMMENT '是否启用',
  `sex` tinyint NOT NULL DEFAULT 0 COMMENT '0：保密 1：男 2：女',
  `head` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `birthday` date NULL DEFAULT '1000-01-01' COMMENT '生日',
  `tel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电话号码',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  `hospital_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '0总管理员1..医院id',
  `doctor_group_id` int NOT NULL DEFAULT 0 COMMENT '医生团队ID',
  `check_company_id` int NOT NULL DEFAULT 0 COMMENT '体检机构id',
  `qywechat_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业微信id',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `phone_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '手机类型0未知 1安卓 2苹果 ',
  `cid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '应用id',
  `user_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户职称',
  `c_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医师客户端版本号',
  `id_card_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'id card no',
  `quick_ask` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '快速问诊',
  `quick_notice` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '快速问诊通知',
  `s_depart` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '科室id',
  `xcx_openid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小程序openid',
  `is_patient_register` int NOT NULL DEFAULT 0 COMMENT '是否开通患者报到,0未开通,1已开通',
  `depart_id` int NOT NULL DEFAULT 0 COMMENT '管理科室',
  `quick_type` int UNSIGNED NOT NULL DEFAULT 6 COMMENT '6=9.9,10=19.9',
  `can_quick_ask` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '0无权限开启快问快答1有权限',
  `login_exp_time` int NULL DEFAULT 0 COMMENT '登录过期时间',
  `wechat_user_id` int NOT NULL DEFAULT 0 COMMENT '微信用户id',
  `doctor_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理医生',
  `is_give_number` int NULL DEFAULT 1 COMMENT '是否赠送次数0否1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  INDEX `user_role_id`(`role_id` ASC) USING BTREE,
  CONSTRAINT `user_role_id` FOREIGN KEY (`role_id`) REFERENCES `api_role` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 6041 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_address
-- ----------------------------
DROP TABLE IF EXISTS `api_user_address`;
CREATE TABLE `api_user_address`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `phone` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '电话',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '地址(省市县)',
  `detail_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '详细地址',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1466 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户地址信息' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_favorite
-- ----------------------------
DROP TABLE IF EXISTS `api_user_favorite`;
CREATE TABLE `api_user_favorite`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NULL DEFAULT 0 COMMENT '用户ID',
  `wechat_id` int NULL DEFAULT 0 COMMENT '微信ID',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '收藏类型1：医院 2：在线问诊医师团队',
  `objkey` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主键key',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_fav_user_id`(`user_id` ASC) USING BTREE,
  INDEX `index_fav_wechat_id`(`wechat_id` ASC) USING BTREE,
  INDEX `api_user_favorite_objkey_IDX`(`objkey` ASC, `user_id` ASC, `type` ASC) USING BTREE,
  INDEX `idx_create_user`(`create_time` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_create_wechat`(`create_time` ASC, `wechat_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4392967 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_user_group
-- ----------------------------
DROP TABLE IF EXISTS `api_user_group`;
CREATE TABLE `api_user_group`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户表主键',
  `group_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '团队表主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5710 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_info
-- ----------------------------
DROP TABLE IF EXISTS `api_user_info`;
CREATE TABLE `api_user_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '电话号',
  `password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `create_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建时间',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态',
  `ask_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '在线咨询的状态',
  `ask_order_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '正在咨询的订单编号',
  `sign_count` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '连续签到次数',
  `sign_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次签到时间',
  `sign_score` smallint NOT NULL DEFAULT 0 COMMENT '签到积分',
  `bind_status` tinyint NULL DEFAULT 1 COMMENT '手机绑定状态',
  `idcard` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `name` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台用户标识',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id` ASC) USING BTREE,
  INDEX `index_mobile`(`mobile` ASC) USING BTREE COMMENT '用户手机号索引'
) ENGINE = InnoDB AUTO_INCREMENT = 100000034 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户信息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_msg
-- ----------------------------
DROP TABLE IF EXISTS `api_user_msg`;
CREATE TABLE `api_user_msg`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '修改时间',
  `user_id` int NOT NULL COMMENT '用户id',
  `type` int NOT NULL DEFAULT 1 COMMENT '分类 1退款2拒绝',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台用户自定义拒接语句' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_user_subscription
-- ----------------------------
DROP TABLE IF EXISTS `api_user_subscription`;
CREATE TABLE `api_user_subscription`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `subscription_type` int NOT NULL,
  `d1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `d2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `d3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `d4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `d5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `api_user_subscription_user_id_IDX`(`user_id` ASC, `subscription_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 178431 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_third
-- ----------------------------
DROP TABLE IF EXISTS `api_user_third`;
CREATE TABLE `api_user_third`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `platform` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方应用',
  `platmobile` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方手机号',
  `openid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方唯一ID',
  `wechat_open_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
  `openname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方会员昵称',
  `access_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'AccessToken',
  `refresh_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'RefreshToken',
  `expires_in` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效期',
  `createtime` int UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `logintime` int UNSIGNED NULL DEFAULT NULL COMMENT '登录时间',
  `expiretime` int UNSIGNED NULL DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `platform`(`platform` ASC, `openid` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC, `platform` ASC) USING BTREE,
  INDEX `idx_wechat_openid`(`wechat_open_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107176 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方登录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_user_unbound
-- ----------------------------
DROP TABLE IF EXISTS `api_user_unbound`;
CREATE TABLE `api_user_unbound`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL COMMENT '类型1就诊人 2黑名单',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `patient_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电话',
  `ID_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证',
  `user_id` int NOT NULL COMMENT '用户id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  `status` tinyint(1) NOT NULL COMMENT '状态0待审核 1成功 2失败 3取消',
  `remarke` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '情况说明',
  `patient_id` int NULL DEFAULT NULL COMMENT 'type=1为就诊人id type=2为黑名单id',
  `explain` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明选项',
  `remove_user_id` int NULL DEFAULT NULL COMMENT '移除人',
  `cancel_user_id` int NULL DEFAULT NULL,
  `cancel_time` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_userid_type`(`user_id` ASC, `type` ASC) USING BTREE COMMENT 'create by DAS-d205934b-1c5d-4f77-aa96-d3a7a6407e69'
) ENGINE = InnoDB AUTO_INCREMENT = 284632 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '就诊人及黑名单解绑申请' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_vaccine_appointment
-- ----------------------------
DROP TABLE IF EXISTS `api_vaccine_appointment`;
CREATE TABLE `api_vaccine_appointment`  (
  `id` int NOT NULL,
  `card_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_variable
-- ----------------------------
DROP TABLE IF EXISTS `api_variable`;
CREATE TABLE `api_variable`  (
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变量',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
  `input_types` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'text' COMMENT '输入框类型 ',
  `check` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'required',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义变量' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_variable_type
-- ----------------------------
DROP TABLE IF EXISTS `api_variable_type`;
CREATE TABLE `api_variable_type`  (
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义变量类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_version
-- ----------------------------
DROP TABLE IF EXISTS `api_version`;
CREATE TABLE `api_version`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `down_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '下载地址',
  `upgrade` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0不强升1强升',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_video
-- ----------------------------
DROP TABLE IF EXISTS `api_video`;
CREATE TABLE `api_video`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `speaker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主讲人',
  `start_time` int NOT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '结束时间',
  `depart` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室',
  `depart_id` int NOT NULL DEFAULT 0 COMMENT '科室id',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `video_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频链接',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶 0否1是',
  `hos_id` int NOT NULL COMMENT '医院code',
  `type` int NOT NULL COMMENT '分类',
  `title_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称',
  `show_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示链接',
  `show_time` int NOT NULL COMMENT '显示时间',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `applet_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序appid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 480 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播列表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_video_article
-- ----------------------------
DROP TABLE IF EXISTS `api_video_article`;
CREATE TABLE `api_video_article`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `speaker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主讲人',
  `create_time` int NOT NULL COMMENT '添加时间',
  `click` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1启用',
  `update_time` int NOT NULL COMMENT '修改时间',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶 0否1是',
  `hos_id` int NOT NULL COMMENT '医院code',
  `type` int NOT NULL COMMENT '分类',
  `title_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情',
  `is_recommend` int NOT NULL DEFAULT 0 COMMENT '是否推荐0否1是',
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 209 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '硬核文章' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_video_type
-- ----------------------------
DROP TABLE IF EXISTS `api_video_type`;
CREATE TABLE `api_video_type`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名',
  `create_time` int NOT NULL COMMENT '添加时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1正常',
  `update_time` int NOT NULL COMMENT '修改时间',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_video_vr
-- ----------------------------
DROP TABLE IF EXISTS `api_video_vr`;
CREATE TABLE `api_video_vr`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名',
  `create_time` int NOT NULL COMMENT '添加时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1正常',
  `update_time` int NOT NULL COMMENT '修改时间',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `hos_id` int NOT NULL COMMENT '医院code',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'vr地图' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wechat
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat`;
CREATE TABLE `api_wechat`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `headimgurl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `sex` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1男 2女 0未知',
  `addtime` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '添加时间',
  `scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '场景  默认为空',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '数据状态',
  `user_id` int NULL DEFAULT 0 COMMENT '对应user_info里面的ID',
  `subscribe` tinyint NOT NULL DEFAULT 1 COMMENT '是否关注',
  `updatetime` int NULL DEFAULT 0 COMMENT '更新时间',
  `unionid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'UnionID',
  `mini_openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '小程序openid',
  `imp_unionid_time` int NULL DEFAULT 0 COMMENT '采集unionid时间',
  `subscribe_time` int NULL DEFAULT 0 COMMENT '关注时间',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户所在城市',
  `country` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户所在国家',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户所在省份',
  `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '公众号运营者对粉丝的备注',
  `groupid` int NULL DEFAULT 0 COMMENT '用户所在的分组ID',
  `subscribe_scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户关注的渠道来源',
  `qr_scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '二维码扫码场景',
  `qr_scene_str` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '二维码扫码场景描述',
  `new_openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '新公众号openid',
  `new_subscribe` tinyint NULL DEFAULT 0 COMMENT '新公众号关注',
  `dzj_openid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大专家订阅号openid',
  `dzj_subscribe` tinyint NULL DEFAULT 0 COMMENT '大专家订阅号关注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid_index`(`openid` ASC) USING BTREE,
  INDEX `user_id_index`(`user_id` ASC) USING BTREE,
  INDEX `scene_index`(`scene` ASC) USING BTREE,
  INDEX `subscribe_index`(`subscribe` ASC) USING BTREE,
  INDEX `idx_addtime_scene_subscribe`(`addtime` ASC, `scene` ASC, `subscribe` ASC) USING BTREE,
  INDEX `mini_openid_index`(`mini_openid` ASC) USING BTREE,
  INDEX `unionid_index`(`unionid` ASC) USING BTREE,
  INDEX `dzj_openid_IDX`(`dzj_openid` ASC) USING BTREE,
  INDEX `api_wechat_new_subscribe_IDX`(`new_subscribe` ASC) USING BTREE,
  INDEX `api_wechat_qr_scene_str_IDX`(`qr_scene_str` ASC) USING BTREE,
  INDEX `idx_subscribetime_newopenid_qrscenestr_newsubscribe`(`subscribe_time` ASC, `new_openid` ASC, `qr_scene_str` ASC, `new_subscribe` ASC) USING BTREE,
  INDEX `idx_newopenid_scene_newsubscribe`(`new_openid` ASC, `scene` ASC, `new_subscribe` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100000016 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_wechat_copy
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_copy`;
CREATE TABLE `api_wechat_copy`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `headimgurl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `sex` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1男 2女 0未知',
  `addtime` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '添加时间',
  `scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '场景  默认为空',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '数据状态',
  `user_id` int NULL DEFAULT 0 COMMENT '对应user_info里面的ID',
  `subscribe` tinyint NOT NULL DEFAULT 1 COMMENT '是否关注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_openid`(`openid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 578248 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_wechat_openid
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_openid`;
CREATE TABLE `api_wechat_openid`  (
  `id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ID',
  `openid` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'openid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信服务号粉丝openid表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wechat_pushnews_msg
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_pushnews_msg`;
CREATE TABLE `api_wechat_pushnews_msg`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `media_id` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源ID',
  `msg_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送任务ID',
  `status` int NOT NULL COMMENT '状态\n-1 - 已发送并且已经删除详情\n0  - 未发送已撤销发送请求\n1  - 已提交发送请求待审核\n2  - 审核通过已发送',
  `log` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务号推送图文消息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wechat_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_tag`;
CREATE TABLE `api_wechat_tag`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名',
  `status` int NOT NULL COMMENT '状态 1-启用 0-停用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户标签表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wechat_uidnull
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_uidnull`;
CREATE TABLE `api_wechat_uidnull`  (
  `id` int UNSIGNED NOT NULL DEFAULT 0,
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wechat_usertag
-- ----------------------------
DROP TABLE IF EXISTS `api_wechat_usertag`;
CREATE TABLE `api_wechat_usertag`  (
  `id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `openid` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tag_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户标签表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wx_bill
-- ----------------------------
DROP TABLE IF EXISTS `api_wx_bill`;
CREATE TABLE `api_wx_bill`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tradetime` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易时间',
  `ghid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众账号ID',
  `mchid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户号',
  `submch` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '子商户号',
  `deviceid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备号',
  `wxorder` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信订单号',
  `bzorder` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户订单号',
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户标识',
  `tradetype` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易类型',
  `tradestatus` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易状态',
  `bank` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '付款银行',
  `currency` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '货币种类',
  `totalmoney` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '总金额',
  `redpacketmoney` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代金券或立减优惠金额',
  `wxrefund` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信退款单号',
  `bzrefund` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户退款单号',
  `refundmoney` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款金额',
  `redpacketrefund` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代金券或立减优惠退款金额',
  `refundtype` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款类型',
  `refundstatus` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款状态',
  `productname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
  `bzdatapacket` varchar(127) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户数据包',
  `fee` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手续费',
  `rate` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '费率',
  `create_time` int NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35708568 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信账单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for api_wx_bill_07
-- ----------------------------
DROP TABLE IF EXISTS `api_wx_bill_07`;
CREATE TABLE `api_wx_bill_07`  (
  `id` int NOT NULL DEFAULT 0,
  `tradetime` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易时间',
  `ghid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众账号ID',
  `mchid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户号',
  `submch` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '子商户号',
  `deviceid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备号',
  `wxorder` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信订单号',
  `bzorder` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户订单号',
  `openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户标识',
  `tradetype` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易类型',
  `tradestatus` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交易状态',
  `bank` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '付款银行',
  `currency` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '货币种类',
  `totalmoney` decimal(10, 2) NOT NULL COMMENT '总金额',
  `redpacketmoney` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代金券或立减优惠金额',
  `wxrefund` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信退款单号',
  `bzrefund` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户退款单号',
  `refundmoney` decimal(10, 2) NOT NULL COMMENT '退款金额',
  `redpacketrefund` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代金券或立减优惠退款金额',
  `refundtype` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款类型',
  `refundstatus` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款状态',
  `productname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
  `bzdatapacket` varchar(127) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户数据包',
  `fee` decimal(10, 2) NOT NULL COMMENT '手续费',
  `rate` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '费率',
  `create_time` int NOT NULL COMMENT '添加时间',
  INDEX `index_bzorder`(`bzorder` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wxgroup_class
-- ----------------------------
DROP TABLE IF EXISTS `api_wxgroup_class`;
CREATE TABLE `api_wxgroup_class`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `ref_class_id` int NULL DEFAULT 0 COMMENT '关联直播群课程ID',
  `class_type` tinyint(1) NULL DEFAULT 1 COMMENT '类型（1：微信进群，2：语音包）',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程名称',
  `teacher` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '讲师',
  `title_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题图片',
  `home_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '首页推荐宣传图',
  `introduce` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '简介',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `detail_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详情介绍连接',
  `base_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `package_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '打包套餐价',
  `package_title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '套餐价标题',
  `appoint_num` int NOT NULL DEFAULT 0 COMMENT '预约人数',
  `view_base_num` int NOT NULL DEFAULT 0 COMMENT '显示预约基数',
  `sort` tinyint NOT NULL COMMENT '排序号（越小越靠前）',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态（-1:已删除，0：待开通，1：预购中）',
  `create_time` int NOT NULL DEFAULT 0,
  `update_time` int NOT NULL DEFAULT 0,
  `course_detail` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '课程详情',
  `home_recommend` int NOT NULL DEFAULT 0 COMMENT '首页推荐顺序值,升序排列,默认0不推荐<br>4 热点内容 &nbsp&nbsp&nbsp&nbsp&nbsp  5 健康直播  &nbsp&nbsp&nbsp&nbsp&nbsp6医生大课堂&nbsp&nbsp&nbsp&nbsp&nbsp10 睡眠专区 <br> 11 眼科专区&nbsp&nbsp&nbsp&nbsp&nbsp 12  神经外科 &nbsp&nbsp&nbsp&nbsp&nbsp 13  妇科专区文章<br>\r\n14  妇科专区直播 &nbsp&nbsp&nbsp&nbsp&nbsp\r\n16 昆华消化内科文章\r\n<br>\r\n17 昆华消化内科直播&nbsp&nbsp&nbsp&nbsp&nbsp  \r\n18 昆华心律失常专区 \r\n<br>\r\n19 云大心律失常专区\r\n&nbsp&nbsp&nbsp&nbsp&nbsp\r\n20 肿瘤专区\r\n<br>\r\n21 昆华皮肤科直播\r\n&nbsp&nbsp&nbsp&nbsp&nbsp\r\n22 昆华皮肤科文章\r\n<br>\r\n23 昆华遗传\r\n&nbsp&nbsp&nbsp&nbsp&nbsp \r\n24 省三妇科',
  `time_des` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '课程时间描述',
  `index_type` int NOT NULL DEFAULT 0 COMMENT '首页分类 1常见疾病 2 专科专病',
  `label_type` int NOT NULL DEFAULT 0 COMMENT '标签分类',
  `hos_id` int NOT NULL COMMENT '医院code',
  `is_recommend` int NOT NULL DEFAULT 0 COMMENT '推荐学习0否1是',
  `applet_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '小程序appid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信课程表\r\n' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wxgroup_label
-- ----------------------------
DROP TABLE IF EXISTS `api_wxgroup_label`;
CREATE TABLE `api_wxgroup_label`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名',
  `create_time` int NOT NULL COMMENT '添加时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int NOT NULL COMMENT '状态0禁用1正常',
  `update_time` int NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wxgroup_order
-- ----------------------------
DROP TABLE IF EXISTS `api_wxgroup_order`;
CREATE TABLE `api_wxgroup_order`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单编号',
  `wuser_id` int NOT NULL COMMENT '微信用户ID',
  `wuser_nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信昵称',
  `class_type` tinyint(1) NULL DEFAULT 1 COMMENT '课程类型1：群直播，2：语音包',
  `class_id` int NOT NULL COMMENT '课程ID',
  `time_id` int NOT NULL COMMENT '课时ID',
  `class_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程名称',
  `remark` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '购买说明',
  `teacher` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '讲师',
  `start_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程开始日期',
  `start_time` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程开始时间',
  `time` int NOT NULL,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `from_channel` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '推荐渠道',
  `from_uid` int NULL DEFAULT 0 COMMENT '推荐用户ID',
  `is_reminded` tinyint(1) NULL DEFAULT 0 COMMENT '是否提醒',
  `is_join` tinyint(1) NULL DEFAULT 0 COMMENT '是否入群,0未加入,1已加入',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_wxgroup_order_wuser_id_IDX`(`wuser_id` ASC, `start_date` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8377 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信课程订单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wxgroup_time
-- ----------------------------
DROP TABLE IF EXISTS `api_wxgroup_time`;
CREATE TABLE `api_wxgroup_time`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `ref_time_id` int NULL DEFAULT 0 COMMENT '关联直播群课程课时ID',
  `class_id` int NOT NULL COMMENT '课程ID',
  `start_date` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程开始日期',
  `start_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程开始时间',
  `time` int NOT NULL COMMENT '课程时长',
  `price` decimal(10, 2) NOT NULL,
  `max_num` int NOT NULL COMMENT '最大预约人数',
  `appoint_num` int NOT NULL DEFAULT 0 COMMENT '已预约人数',
  `qrcode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '二维码',
  `sort` tinyint(1) NOT NULL COMMENT '排序号',
  `status` tinyint(1) NOT NULL COMMENT '状态（-1:已删除，0：待开通，1：预购中）',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '课时备注',
  `create_time` int NOT NULL,
  `update_time` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 92 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信群课时' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_wxgroup_time_chapter
-- ----------------------------
DROP TABLE IF EXISTS `api_wxgroup_time_chapter`;
CREATE TABLE `api_wxgroup_time_chapter`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `class_id` int NOT NULL DEFAULT 0 COMMENT '课程ID',
  `time_id` int NOT NULL DEFAULT 0 COMMENT '课时ID',
  `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `is_free` tinyint(1) NULL DEFAULT 0 COMMENT '是否免费',
  `audio_src` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '音频源',
  `play_time` int NULL DEFAULT 0 COMMENT '播放时长',
  `sort` int NULL DEFAULT 0 COMMENT '排序（越小越靠前）',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 0：禁用， 1：启用',
  `create_time` int NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_xinguan_notify
-- ----------------------------
DROP TABLE IF EXISTS `api_xinguan_notify`;
CREATE TABLE `api_xinguan_notify`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `dep_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for api_yg_enroll_activity
-- ----------------------------
DROP TABLE IF EXISTS `api_yg_enroll_activity`;
CREATE TABLE `api_yg_enroll_activity`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `age` int NOT NULL COMMENT '年龄',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电话',
  `ID_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
  `gestational_age` int NOT NULL COMMENT '孕周',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保健卡图片',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地址',
  `create_time` int NOT NULL COMMENT '添加时间',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领取码',
  `address_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地址详情',
  `receive_time` date NOT NULL COMMENT '领取时间',
  `number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保健卡编号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1正常 0失效 2已领取',
  `work_id` int NULL DEFAULT NULL COMMENT '工作人员id',
  `update_time` int NULL DEFAULT NULL COMMENT '领取时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ID_card`(`ID_card` ASC) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id` ASC) USING BTREE,
  UNIQUE INDEX `number`(`number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2020 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '婴格活动报名表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for base_doctor_depart_info
-- ----------------------------
DROP TABLE IF EXISTS `base_doctor_depart_info`;
CREATE TABLE `base_doctor_depart_info`  (
  `id` bigint NOT NULL,
  `doctor_info_id` bigint NOT NULL,
  `depart_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `hospital_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `his_depart_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `hospital_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `his_doctor_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `hospital_type` int NOT NULL,
  `enable` int NOT NULL DEFAULT 1,
  `sort` int NOT NULL DEFAULT 255,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_doc_id`(`doctor_info_id` ASC) USING BTREE,
  INDEX `base_doctor_depart_info_hospital_id_IDX`(`hospital_id` ASC, `his_depart_id` ASC, `his_doctor_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for base_doctor_function
-- ----------------------------
DROP TABLE IF EXISTS `base_doctor_function`;
CREATE TABLE `base_doctor_function`  (
  `id` bigint NOT NULL,
  `doctor_info_id` bigint NOT NULL,
  `service_type` int NOT NULL COMMENT '1挂号2问诊3住院4报到',
  `enable` int NOT NULL,
  `his_hos_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `his_dep_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `his_doc_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `ask_group_id` int NULL DEFAULT NULL,
  `ask_doc_id` int NULL DEFAULT 0 COMMENT '患者报到指定医生',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for base_doctor_info
-- ----------------------------
DROP TABLE IF EXISTS `base_doctor_info`;
CREATE TABLE `base_doctor_info`  (
  `id` bigint NOT NULL COMMENT 'pk',
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '医生姓名',
  `level_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职称',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像地址',
  `summary` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '医生简介',
  `good_at` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '医生擅长',
  `id_card_no` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDXbase_doctor_info_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '此处的每一条数据，对应显示中一个独立的医生（不管该医生隶属于几个医院）' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for base_online_ask_doctor_rel
-- ----------------------------
DROP TABLE IF EXISTS `base_online_ask_doctor_rel`;
CREATE TABLE `base_online_ask_doctor_rel`  (
  `id` bigint NOT NULL,
  `doctor_info_id` bigint NOT NULL,
  `group_id` int NOT NULL,
  `userId` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for base_wait_handle_item
-- ----------------------------
DROP TABLE IF EXISTS `base_wait_handle_item`;
CREATE TABLE `base_wait_handle_item`  (
  `id` bigint NOT NULL,
  `hos_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `doc_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `doc_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `doctor_type` int NOT NULL,
  `dep_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `dep_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `hos_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for check_package_label_middle
-- ----------------------------
DROP TABLE IF EXISTS `check_package_label_middle`;
CREATE TABLE `check_package_label_middle`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `lable_id` int NOT NULL,
  `package_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for com_record
-- ----------------------------
DROP TABLE IF EXISTS `com_record`;
CREATE TABLE `com_record`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'pk',
  `hos_id` int NOT NULL COMMENT '医院id  hospital表中的hospital_code',
  `hos_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `dep_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `doc_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医生名称',
  `appoint_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '预约时间',
  `jz_time_str` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊时间 文字表述',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '-2已退款-1 不需要支付 0 待支付 1 已支付',
  `order_status` tinyint NOT NULL DEFAULT 1 COMMENT '-1已撤销1预约成功',
  `order_from` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单源 1 inner his 2接通his  3 旧的滇医通',
  `appoint_order_num` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预约订单号',
  `order_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'inner_order 和appoint_record的pk',
  `patient_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '病人名',
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '哪位用户的订单用于筛选',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `hos_id`(`hos_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单公用表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for databasechangeloglock
-- ----------------------------
DROP TABLE IF EXISTS `databasechangeloglock`;
CREATE TABLE `databasechangeloglock`  (
  `ID` int NOT NULL,
  `LOCKED` bit(1) NOT NULL,
  `LOCKGRANTED` datetime NULL DEFAULT NULL,
  `LOCKEDBY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for hibernate_sequence
-- ----------------------------
DROP TABLE IF EXISTS `hibernate_sequence`;
CREATE TABLE `hibernate_sequence`  (
  `next_val` bigint NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for province_stop_diagnosis
-- ----------------------------
DROP TABLE IF EXISTS `province_stop_diagnosis`;
CREATE TABLE `province_stop_diagnosis`  (
  `id` bigint NOT NULL,
  `msg_type` int NOT NULL COMMENT '消息类型：1:停诊2:替诊3医院退费',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `dept_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `hos_org_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院编码（省平台）',
  `resource_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生code',
  `hos_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院名',
  `schedule_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排班ID',
  `stop_or_replace_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生名',
  `hos_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院ID（滇医通）',
  `order_status` int NULL DEFAULT NULL COMMENT '订单状态',
  `pay_state` int NULL DEFAULT NULL COMMENT '支付状态',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '停诊任务状态（用于重试）',
  `count` tinyint NOT NULL DEFAULT 0 COMMENT '停诊任务已运行次数（用于重试）',
  `message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '停诊任务运行信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_province_stop_query`(`id` ASC, `hos_id` ASC, `schedule_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '省平台停诊信息' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for spring_lock
-- ----------------------------
DROP TABLE IF EXISTS `spring_lock`;
CREATE TABLE `spring_lock`  (
  `LOCK_KEY` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `REGION` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CLIENT_ID` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `CREATED_DATE` datetime(6) NOT NULL,
  PRIMARY KEY (`LOCK_KEY`, `REGION`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_abnormal_billing
-- ----------------------------
DROP TABLE IF EXISTS `tmp_abnormal_billing`;
CREATE TABLE `tmp_abnormal_billing`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hos_code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院编码',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单号',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付平台单号',
  `patient_id` int NULL DEFAULT NULL COMMENT '就诊人id',
  `patient_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊人姓名',
  `jz_card` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊卡（登记号）',
  `amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '异常账' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_api_doctor_group
-- ----------------------------
DROP TABLE IF EXISTS `tmp_api_doctor_group`;
CREATE TABLE `tmp_api_doctor_group`  (
  `id` int NOT NULL DEFAULT 0,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医师名称',
  `tag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '团队标签',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提问价格',
  `get_money` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '能否提现 1可以0不可以',
  `hospital_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '医院类型1：公立HIS，2：专科，3：诊所',
  `hospital_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院编码',
  `hospital_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院名称',
  `depart_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室编码',
  `depart_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '科室名称',
  `doctor_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生编码',
  `doctor_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生姓名',
  `good_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '擅长',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生介绍',
  `question_tmpl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题模板json',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态1启用0禁用',
  `admin_user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '医师团队负责人',
  `sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序默认0，升序',
  `follow_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注数默认0',
  `qr_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码地址',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频地址',
  `create_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID',
  `create_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者用户ID',
  `update_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `question_base` int NOT NULL DEFAULT 0 COMMENT '提问基数',
  `question_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '提问次数',
  `video_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频个数',
  `work_time_desc` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接诊时间(时间段描述)',
  `auto_refund_exp` int NOT NULL DEFAULT 24 COMMENT '医师未回答自动退款期效',
  `auto_close_exp` int NOT NULL DEFAULT 24 COMMENT '关闭问题期效',
  `max_ask_times` int NOT NULL DEFAULT 2 COMMENT '允许患者追问次数',
  `s_subdepart` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '子科室',
  `s_depart` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '搜索的科室id',
  `work_year` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '开始工作年',
  `rate_income` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '问题分成比例 0-100',
  `question_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题标签',
  `group_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组宣传图',
  `home_recommend` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否首页推荐',
  `home_sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '首页推荐排序',
  `dyt_admin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '滇医通用户',
  `active_sort` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '昨日完成量',
  `notice_start` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知开始时间',
  `notice_end` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知结束时间',
  `comment_num` int NOT NULL DEFAULT 0 COMMENT '评价数',
  `good_comment_rate` float(6, 2) NOT NULL DEFAULT 0.00 COMMENT '好评率',
  `comment_score` float(6, 2) NOT NULL DEFAULT 0.00 COMMENT '评论综合评分',
  `avg_answer_time` int NOT NULL DEFAULT 0 COMMENT '平均等待接单时间(单位:分)',
  `free_clinic` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0不1义诊',
  `free_clinic_start_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '义诊开始时间',
  `free_clinic_end_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '义诊结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  FULLTEXT INDEX `ask_doc_ft_index`(`name`, `tag`, `hospital_name`, `depart_name`, `good_at`) COMMENT '在线问诊医生全文索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '临时医师表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tmp_appoint_record_871058
-- ----------------------------
DROP TABLE IF EXISTS `tmp_appoint_record_871058`;
CREATE TABLE `tmp_appoint_record_871058`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hos_payed_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '延安医院支付完返回的流水号',
  `ext_order_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医院未支付时的流水号',
  `order_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户ID',
  `patient_id` int NOT NULL DEFAULT 0 COMMENT '就诊人ID',
  `jz_card` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '就诊卡号',
  `patient_id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '就诊人身份证号',
  `patient_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊人手机号',
  `hos_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医院ID',
  `dep_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室ID',
  `doc_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生ID',
  `dep_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `doc_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生姓名',
  `schedule_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '排班ID 延安是汉字',
  `queue_sn` int NULL DEFAULT 0 COMMENT '预约序号',
  `yyid` int NOT NULL DEFAULT 0 COMMENT '预约id,流水号',
  `hyid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '号源ID',
  `hzid` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者id/门诊id,当患者要删记录，把user_id保存过来',
  `start_time` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分时段预约开始时间',
  `end_time` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分时段预约结束时间',
  `sch_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '就诊日期',
  `time_type` int NOT NULL COMMENT '1:上午,2:下午,3:中午,4:晚上',
  `jz_start_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊开始时间',
  `jz_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊地址',
  `amt` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `pay_status` int NULL DEFAULT NULL COMMENT '支付状态 (-2:已退款,-1:不需要支付,0:待支付,1:已支付)',
  `create_time` int NOT NULL,
  `update_time` int NULL DEFAULT NULL,
  `cancel_time` int NOT NULL DEFAULT 0 COMMENT '撤销预约的时间',
  `status` int NOT NULL DEFAULT 1 COMMENT '预约状态:(1:预约成功,-1:已撤销,-2已删除)',
  `ghf` decimal(10, 2) NULL DEFAULT NULL COMMENT '挂号费',
  `zjf` decimal(10, 2) NULL DEFAULT NULL COMMENT '专家费',
  `zlf` decimal(10, 2) NULL DEFAULT NULL COMMENT '诊疗费',
  `cancel_times` int NOT NULL DEFAULT 0 COMMENT '撤销次数',
  `his_status` int NOT NULL DEFAULT 0 COMMENT '1:微信支付成功,his支付失败;2:his退款成功,微信退款失败 小系统是就诊状态0未就诊6已就诊',
  `stop_msg_times` int NOT NULL DEFAULT 0 COMMENT '停诊短信通知次数',
  `sign` int NOT NULL DEFAULT 0 COMMENT '就诊签到标识',
  `appoint_channel` int NULL DEFAULT 0 COMMENT '预约渠道标识,2:微信城市服务,1:公众号',
  `cancel_channel` int NOT NULL DEFAULT 0 COMMENT '取消渠道',
  `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道',
  `consultation_status` tinyint NOT NULL DEFAULT 0 COMMENT '会诊 0 待审核 1过审 2 不通过',
  `refund_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_no_index`(`order_no` ASC) USING BTREE,
  INDEX `patient_id_index`(`patient_id` ASC, `hos_id` ASC) USING BTREE,
  INDEX `patient_phone_index`(`patient_phone` ASC) USING BTREE,
  INDEX `patient_name_index`(`patient_name` ASC) USING BTREE,
  INDEX `idx_cancel_time_hos`(`cancel_time` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_sch_date_hos_status`(`sch_date` ASC, `hos_id` ASC, `status` ASC) USING BTREE,
  INDEX `idx_user_hos`(`user_id` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_doc_hos_createtime_dep`(`doc_name` ASC, `hos_id` ASC, `create_time` ASC, `dep_id` ASC) USING BTREE,
  INDEX `idx_channel_createtime_hos`(`channel` ASC, `create_time` ASC, `hos_id` ASC) USING BTREE,
  INDEX `idx_hos_dep_doc_schedule`(`hos_id` ASC, `dep_id` ASC, `doc_id` ASC, `schedule_id` ASC) USING BTREE,
  INDEX `idx_hos_schedule`(`hos_id` ASC, `schedule_id` ASC) USING BTREE,
  INDEX `idx_hosid_createtime_dep`(`hos_id` ASC, `create_time` ASC, `dep_id` ASC) USING BTREE,
  INDEX `idx_patient_id_card_createtime`(`patient_id_card` ASC, `create_time` ASC) USING BTREE,
  INDEX `idx_updatetime`(`update_time` ASC) USING BTREE,
  INDEX `idx_createtime_orderno`(`create_time` ASC, `order_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21270915 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_appoint_statistic
-- ----------------------------
DROP TABLE IF EXISTS `tmp_appoint_statistic`;
CREATE TABLE `tmp_appoint_statistic`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hos_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `dep_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `dep_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `doc_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `doc_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `appoint_count` int NULL DEFAULT NULL,
  `appoint_amt` decimal(18, 4) NULL DEFAULT NULL,
  `statistic_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `h_index`(`hos_id` ASC) USING BTREE,
  INDEX `s_index`(`statistic_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 357712 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_patient
-- ----------------------------
DROP TABLE IF EXISTS `tmp_patient`;
CREATE TABLE `tmp_patient`  (
  `id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  INDEX `tmp_patient_id_IDX`(`id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_patient_1
-- ----------------------------
DROP TABLE IF EXISTS `tmp_patient_1`;
CREATE TABLE `tmp_patient_1`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NULL DEFAULT NULL COMMENT '用户id,对应user_info中的ID',
  `hzid` int NULL DEFAULT 0 COMMENT '患者ID',
  `health_card` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电子健康卡',
  `patient_relation` int NOT NULL DEFAULT 0 COMMENT '和本人的关系 1本人 2父母 3子女 4朋友  0其他 5婴儿',
  `patient_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人姓名',
  `patient_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人身份证',
  `patient_sex` int NOT NULL DEFAULT 1 COMMENT '就诊人性别 1 男 2女',
  `patient_age` int NOT NULL COMMENT '就诊人年龄',
  `patient_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '就诊人手机号',
  `patient_birthday` date NOT NULL COMMENT '出生日期',
  `use_insure_card` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未知1市医保2自费3省医保4农合5异地医保',
  `insure_card_no` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '医保卡号',
  `patient_sfy_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '市妇幼就诊卡',
  `patient_birth_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出生证号',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认就诊人',
  `share_status` tinyint NOT NULL DEFAULT 0 COMMENT '共享状态',
  `add_time` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '添加时间',
  `update_time` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `guarantee_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '监护人姓名',
  `guarantee_id_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '监护人身份证号',
  `guarantee_relation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '监护人关系',
  `patient_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '就诊人地址',
  `card_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '1身份证2护照',
  `is_check` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否手机号验证(0:未验证,1:已验证)',
  `occupations_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称value',
  `occupations_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职业code',
  `nation_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职业名',
  `nation_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '民族code',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `api_patient_patient_card_IDX`(`patient_card` ASC) USING BTREE,
  INDEX `api_patient_patient_phone_IDX`(`patient_phone` ASC) USING BTREE,
  INDEX `wx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19789577 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信用户就诊人表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_wechat_qr
-- ----------------------------
DROP TABLE IF EXISTS `tmp_wechat_qr`;
CREATE TABLE `tmp_wechat_qr`  (
  `id` bigint NOT NULL,
  `qr_str` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tmp_wechat_qr_count
-- ----------------------------
DROP TABLE IF EXISTS `tmp_wechat_qr_count`;
CREATE TABLE `tmp_wechat_qr_count`  (
  `id` bigint NOT NULL,
  `qr_str` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `wechat_count` bigint NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- View structure for all_appoint_record_view
-- ----------------------------
DROP VIEW IF EXISTS `all_appoint_record_view`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `all_appoint_record_view` AS select `api_appoint_record`.`id` AS `id`,`api_appoint_record`.`user_id` AS `user_id`,`api_appoint_record`.`patient_id` AS `patient_id`,`api_appoint_record`.`hos_id` AS `hos_id`,`api_appoint_record`.`dep_id` AS `dep_id`,`api_appoint_record`.`doc_id` AS `doc_id`,`api_appoint_record`.`sch_date` AS `sch_date`,`api_appoint_record`.`time_type` AS `time_type`,`api_appoint_record`.`status` AS `status`,`api_appoint_record`.`pay_status` AS `pay_status`,`api_appoint_record`.`create_time` AS `create_time` from `api_appoint_record` union select `api_appoint_record_no_order_no`.`id` AS `id`,`api_appoint_record_no_order_no`.`user_id` AS `user_id`,`api_appoint_record_no_order_no`.`patient_id` AS `patient_id`,`api_appoint_record_no_order_no`.`hos_id` AS `hos_id`,`api_appoint_record_no_order_no`.`dep_id` AS `dep_id`,`api_appoint_record_no_order_no`.`doc_id` AS `doc_id`,`api_appoint_record_no_order_no`.`sch_date` AS `sch_date`,`api_appoint_record_no_order_no`.`time_type` AS `time_type`,`api_appoint_record_no_order_no`.`status` AS `status`,`api_appoint_record_no_order_no`.`pay_status` AS `pay_status`,`api_appoint_record_no_order_no`.`create_time` AS `create_time` from `api_appoint_record_no_order_no`;

-- ----------------------------
-- View structure for all_depart_view
-- ----------------------------
DROP VIEW IF EXISTS `all_depart_view`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `all_depart_view` AS select 1 AS `hos_type`,`a`.`hos_id` AS `hos_id`,`a`.`dep_id` AS `dep_id`,`a`.`dep_name` AS `dep_name`,`b`.`hospital_name` AS `hos_name` from (`api_depart` `a` join `api_hospital` `b`) where (`a`.`hos_id` = `b`.`hospital_code`) union select 2 AS `hos_type`,`b`.`hospital_code` AS `hos_id`,`a`.`id` AS `dep_id`,`a`.`depart_name` AS `dep_name`,`b`.`hospital_name` AS `hos_name` from (`api_inner_departs` `a` join `api_hospital` `b`) where (`a`.`hospital_id` = `b`.`id`) union select 3 AS `hos_type`,`a`.`department_clinic_id` AS `hos_id`,`a`.`department_id` AS `dep_id`,`a`.`department_name` AS `dep_name`,`b`.`clinic_name` AS `hos_name` from (`api_clinic_depart` `a` join `api_clinic` `b`) where (`a`.`department_clinic_id` = `b`.`clinic_id`);

-- ----------------------------
-- View structure for all_hospital_view
-- ----------------------------
DROP VIEW IF EXISTS `all_hospital_view`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `all_hospital_view` AS select `api_hospital`.`hospital_code` AS `hos_id`,`api_hospital`.`hospital_name` AS `hos_name`,`api_hospital`.`hos_type` AS `hos_type` from `api_hospital` union select `api_clinic`.`clinic_id` AS `hos_id`,`api_clinic`.`clinic_name` AS `hos_name`,3 AS `hos_type` from `api_clinic`;

SET FOREIGN_KEY_CHECKS = 1;
