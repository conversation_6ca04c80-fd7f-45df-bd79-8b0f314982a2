# 快速启动指南

## 系统状态检查

✅ **Nacos服务**: 已启动并运行在端口8848
✅ **Docker容器**: nacos-server正常运行
✅ **公共模块**: Maven编译成功

## 立即开始使用

### 1. 初始化数据库（仅首次运行）

在MySQL中执行以下命令：

```sql
-- 连接到MySQL
mysql -h127.0.0.1 -uroot -pHwc132465

-- 创建数据库
CREATE DATABASE IF NOT EXISTS order_check_result CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE order_check_result;

-- 创建订单检查结果表
CREATE TABLE `order_check_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `pay_trade_no` varchar(64) DEFAULT NULL COMMENT '支付交易号',
  `pay_amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
  `check_status` tinyint NOT NULL DEFAULT 0 COMMENT '检查状态: 0-待检查, 1-检查中, 2-检查完成, 3-检查失败',
  `check_result` tinyint DEFAULT NULL COMMENT '检查结果: 0-不匹配, 1-匹配',
  `external_order_no` varchar(64) DEFAULT NULL COMMENT '外部系统订单号',
  `external_amount` decimal(10,2) DEFAULT NULL COMMENT '外部系统金额',
  `difference_amount` decimal(10,2) DEFAULT NULL COMMENT '金额差异',
  `check_message` text COMMENT '检查详细信息',
  `error_message` text COMMENT '错误信息',
  `check_time` datetime DEFAULT NULL COMMENT '检查时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

### 2. 启动后端服务

打开3个命令行窗口，分别执行：

**窗口1 - 订单查询服务**
```bash
cd order-query-service
mvn spring-boot:run
```

**窗口2 - 订单检查服务**
```bash
cd order-check-service
mvn spring-boot:run
```

**窗口3 - 结果存储服务**
```bash
cd order-result-service
mvn spring-boot:run
```

### 3. 启动前端服务

打开新的命令行窗口：
```bash
cd order-web-frontend
npm install
npm run dev
```

### 4. 访问系统

- **前端应用**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)

## 系统功能

1. **订单列表**: 查看来自`api_mz_pay_order`的订单数据
2. **订单检查**: 点击"检查"按钮验证订单
3. **批量检查**: 选择多个订单进行批量检查
4. **结果查看**: 查看检查结果和详细信息

## 数据映射

- **PayTradeNo** ← `api_mz_pay_order.order_no`
- **PayAmount** ← `api_mz_pay_order.total_price`

## 故障排除

### 如果端口被占用
- 8080: 前端端口，可在vite.config.js中修改
- 8081-8083: 后端服务端口，可在application.yml中修改
- 8848: Nacos端口，可在docker-compose.yml中修改

### 如果数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接信息是否正确
- 检查防火墙设置

### 如果Redis连接失败
- 检查Redis服务是否启动
- 确认Redis密码是否正确

## 技术支持

- 查看详细文档: README.md
- 项目总结: PROJECT_SUMMARY.md
- 手动启动: MANUAL_START.md

---
**状态**: ✅ 系统已就绪，可以开始使用！
