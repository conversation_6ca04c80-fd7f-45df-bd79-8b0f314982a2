@echo off
echo ========================================
echo Docker Stop Script
echo ========================================

echo.
echo 1. Stopping all containers...
docker-compose down

echo.
echo 2. Checking container status...
docker-compose ps

echo.
echo 3. Cleaning up (optional)...
set /p cleanup="Do you want to clean up images and volumes? (y/N): "
if /i "%cleanup%"=="y" (
    echo Removing unused images...
    docker image prune -f
    echo Removing unused volumes...
    docker volume prune -f
    echo Cleanup completed
) else (
    echo Cleanup skipped
)

echo.
echo ========================================
echo All services stopped!
echo ========================================

pause
