import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/orders'
  },
  {
    path: '/orders',
    name: 'OrderList',
    component: () => import('@/views/OrderList.vue'),
    meta: {
      title: '订单列表'
    }
  },
  {
    path: '/results',
    name: 'ResultList',
    component: () => import('@/views/ResultList.vue'),
    meta: {
      title: '检查结果'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 订单检查系统`
  }
  next()
})

export default router
