<template>
  <div class="result-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="检查状态">
          <el-select
            v-model="searchForm.checkStatus"
            placeholder="请选择检查状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待检查" :value="0" />
            <el-option label="检查中" :value="1" />
            <el-option label="检查完成" :value="2" />
            <el-option label="检查失败" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="检查结果">
          <el-select
            v-model="searchForm.checkResult"
            placeholder="请选择检查结果"
            clearable
            style="width: 150px"
          >
            <el-option label="不匹配" :value="0" />
            <el-option label="匹配" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card">
      <el-button type="success" @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
      <el-button type="info" @click="$router.push('/orders')">
        <el-icon><Document /></el-icon>
        返回订单列表
      </el-button>
    </el-card>

    <!-- 结果表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
      >
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column prop="payTradeNo" label="支付交易号" width="180" />
        <el-table-column prop="payAmount" label="支付金额" width="120">
          <template #default="{ row }">
            <span class="price">¥{{ row.payAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="checkStatus" label="检查状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getCheckStatusType(row.checkStatus)">
              {{ getCheckStatusText(row.checkStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="checkResult" label="检查结果" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.checkResult !== null" :type="getCheckResultType(row.checkResult)">
              {{ getCheckResultText(row.checkResult) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="externalAmount" label="外部金额" width="120">
          <template #default="{ row }">
            <span v-if="row.externalAmount" class="price">¥{{ row.externalAmount }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="differenceAmount" label="金额差异" width="120">
          <template #default="{ row }">
            <span v-if="row.differenceAmount" 
                  :class="['price', row.differenceAmount > 0 ? 'positive' : 'negative']">
              {{ row.differenceAmount > 0 ? '+' : '' }}{{ row.differenceAmount }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="checkTime" label="检查时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.checkTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="info"
              size="small"
              @click="handleViewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="检查结果详情"
      width="800px"
    >
      <el-descriptions :column="2" border v-if="currentResult">
        <el-descriptions-item label="订单号">{{ currentResult.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="支付交易号">{{ currentResult.payTradeNo }}</el-descriptions-item>
        <el-descriptions-item label="支付金额">¥{{ currentResult.payAmount }}</el-descriptions-item>
        <el-descriptions-item label="外部金额">
          {{ currentResult.externalAmount ? '¥' + currentResult.externalAmount : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="金额差异">
          {{ currentResult.differenceAmount || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="检查状态">
          <el-tag :type="getCheckStatusType(currentResult.checkStatus)">
            {{ getCheckStatusText(currentResult.checkStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="检查结果">
          <el-tag v-if="currentResult.checkResult !== null" :type="getCheckResultType(currentResult.checkResult)">
            {{ getCheckResultText(currentResult.checkResult) }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="外部订单号">
          {{ currentResult.externalOrderNo || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="检查时间" :span="2">
          {{ formatTime(currentResult.checkTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="检查信息" :span="2">
          {{ currentResult.checkMessage || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2" v-if="currentResult.errorMessage">
          <el-text type="danger">{{ currentResult.errorMessage }}</el-text>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Document, View } from '@element-plus/icons-vue'
import { resultApi } from '@/api/order'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const detailVisible = ref(false)
const currentResult = ref(null)

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  checkStatus: null,
  checkResult: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取检查结果列表
const getResultList = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await resultApi.getResultPage(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取检查结果列表失败:', error)
    ElMessage.error('获取检查结果列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getResultList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    checkStatus: null,
    checkResult: null
  })
  pagination.current = 1
  getResultList()
}

// 刷新
const handleRefresh = () => {
  getResultList()
}

// 查看详情
const handleViewDetail = (row) => {
  currentResult.value = row
  detailVisible.value = true
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  getResultList()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  getResultList()
}

// 获取检查状态类型
const getCheckStatusType = (status) => {
  const statusMap = {
    0: 'info',     // 待检查
    1: 'warning',  // 检查中
    2: 'success',  // 检查完成
    3: 'danger'    // 检查失败
  }
  return statusMap[status] || 'info'
}

// 获取检查状态文本
const getCheckStatusText = (status) => {
  const statusMap = {
    0: '待检查',
    1: '检查中',
    2: '检查完成',
    3: '检查失败'
  }
  return statusMap[status] || '未知'
}

// 获取检查结果类型
const getCheckResultType = (result) => {
  return result === 1 ? 'success' : 'danger'
}

// 获取检查结果文本
const getCheckResultText = (result) => {
  return result === 1 ? '匹配' : '不匹配'
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载
onMounted(() => {
  getResultList()
})
</script>

<style scoped>
.result-list {
  height: 100%;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.action-card .el-button {
  margin-right: 10px;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.price.positive {
  color: #f56c6c;
}

.price.negative {
  color: #67c23a;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
