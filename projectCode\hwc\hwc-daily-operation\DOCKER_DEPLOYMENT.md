# Docker统一部署指南

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端服务       │    │  订单查询服务    │    │  订单检查服务    │
│   (Nginx:80)   │────│   (8081)       │────│   (8082)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│  结果存储服务    │──────────────┘
                        │   (8083)       │
                        └─────────────────┘
                                 │
                    ┌─────────────────────────────┐
                    │        Nacos 注册中心        │
                    │         (8848)             │
                    └─────────────────────────────┘
                                 │
                    ┌─────────────────────────────┐
                    │     MySQL + Redis          │
                    │   (3306)    (6379)        │
                    └─────────────────────────────┘
```

## 服务列表

| 服务名称 | 容器名称 | 端口映射 | 说明 |
|---------|---------|---------|------|
| MySQL | order-check-mysql | 3306:3306 | 数据库服务 |
| Redis | order-check-redis | 6379:6379 | 缓存服务 |
| Nacos | nacos-server | 8848:8848, 9848:9848 | 服务注册中心 |
| 订单查询服务 | order-query-service | 8081:8081 | 查询api_mz_pay_order |
| 订单检查服务 | order-check-service | 8082:8082 | 处理订单检查逻辑 |
| 结果存储服务 | order-result-service | 8083:8083 | 存储检查结果 |
| 前端服务 | order-web-frontend | 8080:80 | Vue3前端界面 |

## 快速部署

### 方式一：一键部署（推荐）

```bash
# 构建并启动所有服务
docker-build.bat

# 停止所有服务
docker-stop.bat
```

### 方式二：手动部署

```bash
# 1. 编译Java项目
mvn clean package -DskipTests

# 2. 构建并启动所有服务
docker-compose up --build -d

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f [service-name]

# 5. 停止服务
docker-compose down
```

## 数据库配置

### 内置MySQL配置
- **地址**: mysql:3306 (容器内) / localhost:3306 (宿主机)
- **用户名**: root
- **密码**: Hwc132465
- **数据库**: 
  - `nacos` - Nacos配置数据
  - `order_check_result` - 检查结果数据

### 外部数据库连接
如需连接外部的`dytkf`数据库(*************:3306)，需要修改配置：

1. 修改`order-query-service`的环境变量：
```yaml
environment:
  - MYSQL_HOST=*************
  - MYSQL_DATABASE=dytkf
  - MYSQL_USERNAME=dyt
  - MYSQL_PASSWORD=dyt
```

2. 在docker-compose.yml中添加网络配置：
```yaml
extra_hosts:
  - "external-db:*************"
```

## 访问地址

- **前端应用**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
- **订单查询服务**: http://localhost:8081
- **订单检查服务**: http://localhost:8082  
- **结果存储服务**: http://localhost:8083

## 健康检查

所有后端服务都配置了健康检查：
```bash
# 检查所有服务状态
docker-compose ps

# 检查特定服务健康状态
docker inspect --format='{{.State.Health.Status}}' order-query-service
```

## 日志管理

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs -f order-query-service

# 查看最近100行日志
docker-compose logs --tail=100 order-web-frontend
```

## 数据持久化

系统使用Docker volumes持久化数据：
- `mysql-data`: MySQL数据文件
- `redis-data`: Redis数据文件

```bash
# 查看数据卷
docker volume ls

# 备份数据卷
docker run --rm -v mysql-data:/data -v $(pwd):/backup alpine tar czf /backup/mysql-backup.tar.gz -C /data .
```

## 扩展配置

### 环境变量配置

可以通过环境变量自定义配置：

```bash
# 设置外部API地址
EXTERNAL_API_URL=http://your-api.com/check

# 设置数据库连接
MYSQL_HOST=your-mysql-host
MYSQL_USERNAME=your-username
MYSQL_PASSWORD=your-password

# 设置Redis连接
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password
```

### 资源限制

在docker-compose.yml中添加资源限制：
```yaml
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '0.5'
    reservations:
      memory: 512M
      cpus: '0.25'
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs [service-name]
   
   # 重启特定服务
   docker-compose restart [service-name]
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | findstr :8080
   
   # 修改docker-compose.yml中的端口映射
   ports:
     - "8090:80"  # 改为其他端口
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose logs mysql
   
   # 进入MySQL容器
   docker-compose exec mysql mysql -uroot -pHwc132465
   ```

4. **内存不足**
   ```bash
   # 调整JVM内存参数
   environment:
     - JAVA_OPTS=-Xms256m -Xmx512m
   ```

### 性能优化

1. **调整JVM参数**
2. **配置数据库连接池**
3. **启用Redis缓存**
4. **配置Nginx缓存**

## 生产环境部署

1. **安全配置**
   - 修改默认密码
   - 配置防火墙规则
   - 启用HTTPS

2. **监控配置**
   - 集成Prometheus
   - 配置Grafana仪表板
   - 设置告警规则

3. **备份策略**
   - 定期备份数据库
   - 备份配置文件
   - 备份应用日志

---

**部署状态**: ✅ Docker统一部署方案已就绪
