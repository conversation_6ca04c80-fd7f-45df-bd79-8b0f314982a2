package com.hwc.order.query.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hwc.common.dto.OrderInfoDTO;
import com.hwc.common.result.PageResult;
import com.hwc.order.query.entity.ApiMzPayOrder;

import java.util.List;

/**
 * 订单查询服务接口
 *
 * <AUTHOR>
 */
public interface OrderQueryService {

    /**
     * 分页查询订单信息
     *
     * @param current     当前页
     * @param size        每页大小
     * @param orderNo     订单号
     * @param patientName 患者姓名
     * @param status      订单状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 分页结果
     */
    PageResult<OrderInfoDTO> getOrderPage(Long current, Long size, String orderNo,
                                          String patientName, Integer status,
                                          Integer startTime, Integer endTime);

    /**
     * 根据订单号查询订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderInfoDTO getOrderByOrderNo(String orderNo);

    /**
     * 查询待检查的订单
     *
     * @param limit 限制数量
     * @return 订单列表
     */
    List<OrderInfoDTO> getPendingCheckOrders(Integer limit);

    /**
     * 转换实体为DTO
     *
     * @param order 订单实体
     * @return 订单DTO
     */
    OrderInfoDTO convertToDTO(ApiMzPayOrder order);
}
