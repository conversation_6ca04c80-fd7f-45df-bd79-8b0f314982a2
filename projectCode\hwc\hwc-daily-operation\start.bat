@echo off
chcp 65001
echo ========================================
echo 分布式订单检查系统启动脚本
echo ========================================

echo.
echo 1. 启动Nacos服务...
docker-compose up -d
if %errorlevel% neq 0 (
    echo Nacos启动失败，请检查Docker是否正常运行
    pause
    exit /b 1
)

echo.
echo 2. 等待Nacos服务启动完成...
timeout /t 30 /nobreak

echo.
echo 3. 编译Maven项目...
call mvn clean install -DskipTests
if %errorlevel% neq 0 (
    echo Maven编译失败
    pause
    exit /b 1
)

echo.
echo 4. 启动后端服务...
echo 启动订单查询服务 (端口: 8081)...
start "Order Query Service" cmd /c "cd order-query-service && mvn spring-boot:run"

timeout /t 10 /nobreak

echo 启动订单检查服务 (端口: 8082)...
start "Order Check Service" cmd /c "cd order-check-service && mvn spring-boot:run"

timeout /t 10 /nobreak

echo 启动结果存储服务 (端口: 8083)...
start "Order Result Service" cmd /c "cd order-result-service && mvn spring-boot:run"

echo.
echo 5. 安装前端依赖...
cd order-web-frontend
call npm install
if %errorlevel% neq 0 (
    echo 前端依赖安装失败
    pause
    exit /b 1
)

echo.
echo 6. 启动前端服务 (端口: 8080)...
start "Frontend" cmd /c "npm run dev"

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo Nacos控制台: http://localhost:8848/nacos
echo 前端应用: http://localhost:8080
echo 订单查询服务: http://localhost:8081
echo 订单检查服务: http://localhost:8082
echo 结果存储服务: http://localhost:8083
echo ========================================

pause
