package com.hwc.order.query;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 订单查询服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hwc.order.query.mapper")
public class OrderQueryApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderQueryApplication.class, args);
    }
}
