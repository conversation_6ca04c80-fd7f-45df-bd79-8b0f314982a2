package com.hwc.order.query.controller;

import com.hwc.common.dto.OrderInfoDTO;
import com.hwc.common.result.PageResult;
import com.hwc.common.result.Result;
import com.hwc.order.query.service.OrderQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单查询控制器
 *
 * <AUTHOR>
 */
@Api(tags = "订单查询接口")
@RestController
@RequestMapping("/api/order/query")
public class OrderQueryController {

    @Autowired
    private OrderQueryService orderQueryService;

    /**
     * 分页查询订单信息
     */
    @ApiOperation("分页查询订单信息")
    @GetMapping("/page")
    public Result<PageResult<OrderInfoDTO>> getOrderPage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("订单号") @RequestParam(required = false) String orderNo,
            @ApiParam("患者姓名") @RequestParam(required = false) String patientName,
            @ApiParam("订单状态") @RequestParam(required = false) Integer status,
            @ApiParam("开始时间") @RequestParam(required = false) Integer startTime,
            @ApiParam("结束时间") @RequestParam(required = false) Integer endTime) {

        PageResult<OrderInfoDTO> result = orderQueryService.getOrderPage(
                current, size, orderNo, patientName, status, startTime, endTime);
        return Result.success(result);
    }

    /**
     * 根据订单号查询订单信息
     */
    @ApiOperation("根据订单号查询订单信息")
    @GetMapping("/detail/{orderNo}")
    public Result<OrderInfoDTO> getOrderDetail(
            @ApiParam("订单号") @PathVariable String orderNo) {

        OrderInfoDTO order = orderQueryService.getOrderByOrderNo(orderNo);
        if (order == null) {
            return Result.error("订单不存在");
        }
        return Result.success(order);
    }

    /**
     * 查询待检查的订单
     */
    @ApiOperation("查询待检查的订单")
    @GetMapping("/pending")
    public Result<List<OrderInfoDTO>> getPendingCheckOrders(
            @ApiParam("限制数量") @RequestParam(defaultValue = "100") Integer limit) {

        List<OrderInfoDTO> orders = orderQueryService.getPendingCheckOrders(limit);
        return Result.success(orders);
    }
}
