@echo off
chcp 65001
echo ========================================
echo 分布式订单检查系统停止脚本
echo ========================================

echo.
echo 1. 停止Java进程...
taskkill /f /im java.exe 2>nul
if %errorlevel% equ 0 (
    echo Java进程已停止
) else (
    echo 没有找到Java进程
)

echo.
echo 2. 停止Node.js进程...
taskkill /f /im node.exe 2>nul
if %errorlevel% equ 0 (
    echo Node.js进程已停止
) else (
    echo 没有找到Node.js进程
)

echo.
echo 3. 停止Docker容器...
docker-compose down
if %errorlevel% equ 0 (
    echo Docker容器已停止
) else (
    echo Docker容器停止失败或未运行
)

echo.
echo ========================================
echo 系统已停止！
echo ========================================

pause
