-- 创建订单检查结果数据库
CREATE DATABASE IF NOT EXISTS order_check_result CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE order_check_result;

-- 订单检查结果表
CREATE TABLE `order_check_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `pay_trade_no` varchar(64) DEFAULT NULL COMMENT '支付交易号(来自api_mz_pay_order的order_no)',
  `pay_amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额(来自api_mz_pay_order的total_price)',
  `check_status` tinyint NOT NULL DEFAULT 0 COMMENT '检查状态: 0-待检查, 1-检查中, 2-检查完成, 3-检查失败',
  `check_result` tinyint DEFAULT NULL COMMENT '检查结果: 0-不匹配, 1-匹配',
  `external_order_no` varchar(64) DEFAULT NULL COMMENT '外部系统订单号',
  `external_amount` decimal(10,2) DEFAULT NULL COMMENT '外部系统金额',
  `difference_amount` decimal(10,2) DEFAULT NULL COMMENT '金额差异',
  `check_message` text COMMENT '检查详细信息',
  `error_message` text COMMENT '错误信息',
  `check_time` datetime DEFAULT NULL COMMENT '检查时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_pay_trade_no` (`pay_trade_no`),
  KEY `idx_check_status` (`check_status`),
  KEY `idx_check_result` (`check_result`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单检查结果表';

-- 订单检查日志表
CREATE TABLE `order_check_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `check_type` varchar(20) NOT NULL COMMENT '检查类型',
  `check_step` varchar(50) NOT NULL COMMENT '检查步骤',
  `step_status` tinyint NOT NULL COMMENT '步骤状态: 0-失败, 1-成功',
  `step_message` text COMMENT '步骤信息',
  `request_data` json COMMENT '请求数据',
  `response_data` json COMMENT '响应数据',
  `execution_time` int DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_check_type` (`check_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单检查日志表';

-- 系统配置表
CREATE TABLE `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型: STRING, NUMBER, BOOLEAN, JSON',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用: 0-禁用, 1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('external.api.url', 'http://example.com/api/order/check', '外部API检查地址', 'STRING'),
('external.api.timeout', '30000', '外部API超时时间(毫秒)', 'NUMBER'),
('external.api.retry.count', '3', '外部API重试次数', 'NUMBER'),
('check.batch.size', '100', '批量检查大小', 'NUMBER'),
('check.auto.enabled', 'false', '是否启用自动检查', 'BOOLEAN');
