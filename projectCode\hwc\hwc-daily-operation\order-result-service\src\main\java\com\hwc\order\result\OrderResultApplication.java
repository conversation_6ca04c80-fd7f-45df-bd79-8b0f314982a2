package com.hwc.order.result;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 订单结果存储服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hwc.order.result.mapper")
public class OrderResultApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderResultApplication.class, args);
    }
}
