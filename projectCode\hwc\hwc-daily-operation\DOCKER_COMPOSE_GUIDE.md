# Docker Compose 使用指南

## 🐳 系统架构

本项目使用Docker Compose编排7个容器服务：

```yaml
services:
  mysql:           # MySQL数据库 :3306
  redis:           # Redis缓存 :6379  
  nacos:           # Nacos注册中心 :8848
  order-query-service:     # 订单查询服务 :8081
  order-check-service:     # 订单检查服务 :8082
  order-result-service:    # 结果存储服务 :8083
  order-web-frontend:      # 前端服务 :8080
```

## 🚀 快速启动

### 1. 一键启动所有服务
```bash
# 构建并启动所有服务（推荐）
docker-compose up --build -d

# 或者使用提供的脚本
docker-build.bat
```

### 2. 查看服务状态
```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f order-query-service
```

### 3. 访问系统
- **前端应用**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)

## 🛠️ 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart order-query-service

# 停止特定服务
docker-compose stop order-query-service

# 启动特定服务
docker-compose start order-query-service
```

### 构建管理
```bash
# 重新构建所有镜像
docker-compose build

# 重新构建特定服务镜像
docker-compose build order-query-service

# 强制重新构建并启动
docker-compose up --build --force-recreate -d
```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 实时查看日志
docker-compose logs -f

# 查看最近100行日志
docker-compose logs --tail=100

# 查看特定服务日志
docker-compose logs -f mysql
docker-compose logs -f nacos
docker-compose logs -f order-query-service
```

### 数据管理
```bash
# 查看数据卷
docker volume ls

# 备份MySQL数据
docker-compose exec mysql mysqldump -uroot -pHwc132465 --all-databases > backup.sql

# 进入MySQL容器
docker-compose exec mysql mysql -uroot -pHwc132465

# 进入Redis容器
docker-compose exec redis redis-cli -a HdkcRedis123!@#
```

## 🔧 配置说明

### 环境变量
可以通过修改`docker-compose.yml`中的环境变量来调整配置：

```yaml
environment:
  - SPRING_PROFILES_ACTIVE=docker
  - NACOS_SERVER_ADDR=nacos:8848
  - MYSQL_HOST=mysql
  - MYSQL_DATABASE=dytkf
  - REDIS_HOST=redis
```

### 端口映射
```yaml
ports:
  - "8080:80"    # 前端服务
  - "8081:8081"  # 订单查询服务
  - "8082:8082"  # 订单检查服务
  - "8083:8083"  # 结果存储服务
  - "8848:8848"  # Nacos控制台
  - "3306:3306"  # MySQL数据库
  - "6379:6379"  # Redis缓存
```

### 数据持久化
```yaml
volumes:
  mysql-data:    # MySQL数据持久化
  redis-data:    # Redis数据持久化
```

## 🐛 故障排除

### 1. 服务启动失败
```bash
# 查看详细错误信息
docker-compose logs [service-name]

# 检查服务依赖
docker-compose ps
```

### 2. 端口冲突
```bash
# 检查端口占用
netstat -an | findstr :8080

# 修改端口映射
ports:
  - "8090:80"  # 改为其他端口
```

### 3. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -uroot -pHwc132465 -e "SHOW DATABASES;"
```

### 4. 内存不足
```bash
# 调整JVM内存参数
environment:
  - JAVA_OPTS=-Xms256m -Xmx512m
```

## 🔄 更新部署

### 更新代码后重新部署
```bash
# 1. 停止服务
docker-compose down

# 2. 重新编译代码
mvn clean package -DskipTests

# 3. 重新构建并启动
docker-compose up --build -d
```

### 仅更新特定服务
```bash
# 重新构建并重启特定服务
docker-compose up --build -d order-query-service
```

## 📊 监控和维护

### 健康检查
```bash
# 检查容器健康状态
docker-compose ps

# 查看容器详细信息
docker inspect order-query-service
```

### 资源使用
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用
docker system df
```

### 清理资源
```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的容器
docker container prune -f

# 清理未使用的网络
docker network prune -f

# 清理所有未使用资源
docker system prune -f
```

## 🎯 最佳实践

1. **定期备份数据**：备份MySQL和Redis数据
2. **监控日志**：定期查看服务日志
3. **资源清理**：定期清理未使用的Docker资源
4. **版本管理**：为镜像打标签进行版本管理
5. **环境隔离**：不同环境使用不同的compose文件

---

**这就是标准的Docker Compose方案！** 🐳
