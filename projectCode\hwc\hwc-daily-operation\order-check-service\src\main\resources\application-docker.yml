server:
  port: 8082

spring:
  application:
    name: order-check-service
  
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:nacos:8848}
        namespace: public
        group: DEFAULT_GROUP
      config:
        server-addr: ${NACOS_SERVER_ADDR:nacos:8848}
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true

  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:HdkcRedis123!@#}
    database: 2
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# 外部API配置
external:
  api:
    url: ${EXTERNAL_API_URL:http://example.com/api/order/check}
    timeout: ${EXTERNAL_API_TIMEOUT:30000}
    retry:
      count: ${EXTERNAL_API_RETRY_COUNT:3}

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.hwc.order.check: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
