version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: order-check-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=Hwc132465
      - MYSQL_DATABASE=order_check_result
      - MYSQL_USER=hwc
      - MYSQL_PASSWORD=hwc123
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init-database.sql:/docker-entrypoint-initdb.d/init-database.sql
      - ./nacos-mysql.sql:/docker-entrypoint-initdb.d/nacos-mysql.sql
    restart: always
    networks:
      - order-check-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: order-check-redis
    command: redis-server --requirepass HdkcRedis123!@#
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: always
    networks:
      - order-check-network

  # Nacos服务注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-server
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=Hwc132465
      - MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    restart: always
    networks:
      - order-check-network

  # 订单查询服务
  order-query-service:
    build:
      context: .
      dockerfile: order-query-service/Dockerfile
    container_name: order-query-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dytkf
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=Hwc132465
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
    depends_on:
      - mysql
      - redis
      - nacos
    restart: always
    networks:
      - order-check-network

  # 订单检查服务
  order-check-service:
    build:
      context: .
      dockerfile: order-check-service/Dockerfile
    container_name: order-check-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
    depends_on:
      - nacos
      - redis
    restart: always
    networks:
      - order-check-network

  # 结果存储服务
  order-result-service:
    build:
      context: .
      dockerfile: order-result-service/Dockerfile
    container_name: order-result-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=order_check_result
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=Hwc132465
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
    depends_on:
      - mysql
      - redis
      - nacos
    restart: always
    networks:
      - order-check-network

  # 前端服务
  order-web-frontend:
    build:
      context: .
      dockerfile: order-web-frontend/Dockerfile
    container_name: order-web-frontend
    ports:
      - "8080:80"
    depends_on:
      - order-query-service
      - order-check-service
      - order-result-service
    restart: always
    networks:
      - order-check-network

volumes:
  mysql-data:
  redis-data:

networks:
  order-check-network:
    driver: bridge
