version: '3.8'

services:
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-server
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=nacos123
      - MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    restart: always
    networks:
      - order-check-network

  mysql:
    image: mysql:8.0
    container_name: nacos-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=nacos123
      - MYSQL_DATABASE=nacos
      - MYSQL_USER=nacos
      - MYSQL_PASSWORD=nacos123
    ports:
      - "3307:3306"
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./nacos-mysql.sql:/docker-entrypoint-initdb.d/nacos-mysql.sql
    restart: always
    networks:
      - order-check-network

  redis:
    image: redis:7-alpine
    container_name: order-check-redis
    ports:
      - "6379:6379"
    restart: always
    networks:
      - order-check-network

networks:
  order-check-network:
    driver: bridge
