import request from './request'

/**
 * 订单相关API
 */
export const orderApi = {
  /**
   * 分页查询订单列表
   */
  getOrderPage(params) {
    return request({
      url: '/api/order/query/page',
      method: 'get',
      params
    })
  },

  /**
   * 根据订单号查询订单详情
   */
  getOrderDetail(orderNo) {
    return request({
      url: `/api/order/query/detail/${orderNo}`,
      method: 'get'
    })
  },

  /**
   * 查询待检查的订单
   */
  getPendingOrders(limit = 100) {
    return request({
      url: '/api/order/query/pending',
      method: 'get',
      params: { limit }
    })
  },

  /**
   * 单个订单检查
   */
  checkSingleOrder(orderNo) {
    return request({
      url: '/api/order/check/single',
      method: 'post',
      data: { orderNo }
    })
  },

  /**
   * 批量订单检查
   */
  checkBatchOrders(orderNos) {
    return request({
      url: '/api/order/check/batch',
      method: 'post',
      data: { orderNos }
    })
  }
}

/**
 * 检查结果相关API
 */
export const resultApi = {
  /**
   * 分页查询检查结果
   */
  getResultPage(params) {
    return request({
      url: '/api/order/result/page',
      method: 'get',
      params
    })
  },

  /**
   * 根据订单号查询检查结果
   */
  getResultByOrderNo(orderNo) {
    return request({
      url: `/api/order/result/detail/${orderNo}`,
      method: 'get'
    })
  }
}
