package com.hwc.order.query.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwc.order.query.entity.ApiMzPayOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门诊支付订单Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiMzPayOrderMapper extends BaseMapper<ApiMzPayOrder> {

    /**
     * 分页查询订单信息
     *
     * @param page        分页参数
     * @param orderNo     订单号
     * @param patientName 患者姓名
     * @param status      订单状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 订单列表
     */
    @Select("<script>" +
            "SELECT * FROM api_mz_pay_order " +
            "WHERE 1=1 " +
            "<if test='orderNo != null and orderNo != \"\"'>" +
            "AND order_no LIKE CONCAT('%', #{orderNo}, '%') " +
            "</if>" +
            "<if test='patientName != null and patientName != \"\"'>" +
            "AND patient_name LIKE CONCAT('%', #{patientName}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "AND create_time <= #{endTime} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<ApiMzPayOrder> selectOrderPage(Page<ApiMzPayOrder> page,
                                         @Param("orderNo") String orderNo,
                                         @Param("patientName") String patientName,
                                         @Param("status") Integer status,
                                         @Param("startTime") Integer startTime,
                                         @Param("endTime") Integer endTime);

    /**
     * 根据订单号查询订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Select("SELECT * FROM api_mz_pay_order WHERE order_no = #{orderNo}")
    ApiMzPayOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询待检查的订单
     *
     * @param limit 限制数量
     * @return 订单列表
     */
    @Select("SELECT * FROM api_mz_pay_order " +
            "WHERE status = 1 " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<ApiMzPayOrder> selectPendingCheckOrders(@Param("limit") Integer limit);
}
