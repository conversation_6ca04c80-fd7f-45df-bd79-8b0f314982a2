@echo off
echo ========================================
echo Docker Build and Deploy Script
echo ========================================

echo.
echo 1. Stopping existing containers...
docker-compose down

echo.
echo 2. Cleaning up old images...
docker system prune -f

echo.
echo 3. Building Maven projects...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo Maven build failed
    pause
    exit /b 1
)

echo.
echo 4. Building Docker images and starting services...
docker-compose up --build -d

echo.
echo 5. Waiting for services to start...
timeout /t 60 /nobreak

echo.
echo 6. Checking service status...
docker-compose ps

echo.
echo ========================================
echo Docker deployment completed!
echo ========================================
echo Frontend: http://localhost:8080
echo Nacos Console: http://localhost:8848/nacos (nacos/nacos)
echo Order Query Service: http://localhost:8081
echo Order Check Service: http://localhost:8082
echo Order Result Service: http://localhost:8083
echo ========================================

echo.
echo Checking service health...
timeout /t 10 /nobreak

echo Testing frontend...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080' -TimeoutSec 5; Write-Host 'Frontend: OK' } catch { Write-Host 'Frontend: Not Ready' }"

echo Testing Nacos...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8848/nacos' -TimeoutSec 5; Write-Host 'Nacos: OK' } catch { Write-Host 'Nacos: Not Ready' }"

echo.
echo To view logs: docker-compose logs -f [service-name]
echo To stop all services: docker-compose down

pause
