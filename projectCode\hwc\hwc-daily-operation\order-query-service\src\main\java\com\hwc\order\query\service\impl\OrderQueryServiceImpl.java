package com.hwc.order.query.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwc.common.dto.OrderInfoDTO;
import com.hwc.common.result.PageResult;
import com.hwc.order.query.entity.ApiMzPayOrder;
import com.hwc.order.query.mapper.ApiMzPayOrderMapper;
import com.hwc.order.query.service.OrderQueryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class OrderQueryServiceImpl implements OrderQueryService {

    @Autowired
    private ApiMzPayOrderMapper orderMapper;

    @Override
    public PageResult<OrderInfoDTO> getOrderPage(Long current, Long size, String orderNo,
                                                 String patientName, Integer status,
                                                 Integer startTime, Integer endTime) {
        Page<ApiMzPayOrder> page = new Page<>(current, size);
        IPage<ApiMzPayOrder> orderPage = orderMapper.selectOrderPage(page, orderNo, patientName, status, startTime, endTime);

        List<OrderInfoDTO> records = orderPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResult.of(records, orderPage.getTotal(), orderPage.getCurrent(), orderPage.getSize());
    }

    @Override
    public OrderInfoDTO getOrderByOrderNo(String orderNo) {
        ApiMzPayOrder order = orderMapper.selectByOrderNo(orderNo);
        return order != null ? convertToDTO(order) : null;
    }

    @Override
    public List<OrderInfoDTO> getPendingCheckOrders(Integer limit) {
        List<ApiMzPayOrder> orders = orderMapper.selectPendingCheckOrders(limit);
        return orders.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public OrderInfoDTO convertToDTO(ApiMzPayOrder order) {
        if (order == null) {
            return null;
        }

        OrderInfoDTO dto = new OrderInfoDTO();
        BeanUtils.copyProperties(order, dto);

        // 转换时间戳为LocalDateTime
        if (order.getCreateTime() != null) {
            dto.setCreateTime(LocalDateTime.ofInstant(
                    Instant.ofEpochSecond(order.getCreateTime()),
                    ZoneId.systemDefault()));
        }

        if (order.getUpdateTime() != null) {
            dto.setUpdateTime(LocalDateTime.ofInstant(
                    Instant.ofEpochSecond(order.getUpdateTime()),
                    ZoneId.systemDefault()));
        }

        return dto;
    }
}
