<template>
  <div class="order-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="患者姓名">
          <el-input
            v-model="searchForm.patientName"
            placeholder="请输入患者姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待支付" :value="0" />
            <el-option label="支付成功" :value="1" />
            <el-option label="支付失败" :value="-1" />
            <el-option label="已退款" :value="-2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card">
      <el-button
        type="primary"
        @click="handleBatchCheck"
        :disabled="selectedOrders.length === 0"
      >
        <el-icon><Check /></el-icon>
        批量检查 ({{ selectedOrders.length }})
      </el-button>
      <el-button type="success" @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
      <el-button type="info" @click="$router.push('/results')">
        <el-icon><Document /></el-icon>
        查看结果
      </el-button>
    </el-card>

    <!-- 订单表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column prop="patientName" label="患者姓名" width="120" />
        <el-table-column prop="totalPrice" label="金额" width="100">
          <template #default="{ row }">
            <span class="price">¥{{ row.totalPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="depName" label="科室" width="120" />
        <el-table-column prop="docName" label="医生" width="100" />
        <el-table-column prop="billType" label="处方类型" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleSingleCheck(row)"
            >
              <el-icon><Check /></el-icon>
              检查
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="订单详情"
      width="600px"
    >
      <el-descriptions :column="2" border v-if="currentOrder">
        <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="患者姓名">{{ currentOrder.patientName }}</el-descriptions-item>
        <el-descriptions-item label="就诊卡号">{{ currentOrder.jzCard }}</el-descriptions-item>
        <el-descriptions-item label="单据号">{{ currentOrder.billId }}</el-descriptions-item>
        <el-descriptions-item label="金额">¥{{ currentOrder.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="科室">{{ currentOrder.depName }}</el-descriptions-item>
        <el-descriptions-item label="医生">{{ currentOrder.docName }}</el-descriptions-item>
        <el-descriptions-item label="处方类型">{{ currentOrder.billType }}</el-descriptions-item>
        <el-descriptions-item label="开单时间">{{ currentOrder.billTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentOrder.status)">
            {{ getStatusText(currentOrder.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Check, Document, View } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedOrders = ref([])
const detailVisible = ref(false)
const currentOrder = ref(null)

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  patientName: '',
  status: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取订单列表
const getOrderList = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await orderApi.getOrderPage(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getOrderList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    patientName: '',
    status: null
  })
  pagination.current = 1
  getOrderList()
}

// 刷新
const handleRefresh = () => {
  getOrderList()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 单个检查
const handleSingleCheck = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要检查订单 ${row.orderNo} 吗？`,
      '确认检查',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await orderApi.checkSingleOrder(row.orderNo)
    ElMessage.success('订单检查已提交')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('订单检查失败:', error)
    }
  }
}

// 批量检查
const handleBatchCheck = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要检查选中的 ${selectedOrders.value.length} 个订单吗？`,
      '确认批量检查',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const orderNos = selectedOrders.value.map(item => item.orderNo)
    await orderApi.checkBatchOrders(orderNos)
    ElMessage.success('批量检查已提交')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量检查失败:', error)
    }
  }
}

// 查看详情
const handleViewDetail = (row) => {
  currentOrder.value = row
  detailVisible.value = true
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  getOrderList()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  getOrderList()
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    '-1': 'danger',
    '-2': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待支付',
    1: '支付成功',
    '-1': '支付失败',
    '-2': '已退款'
  }
  return statusMap[status] || '未知'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载
onMounted(() => {
  getOrderList()
})
</script>

<style scoped>
.order-list {
  height: 100%;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.action-card .el-button {
  margin-right: 10px;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
