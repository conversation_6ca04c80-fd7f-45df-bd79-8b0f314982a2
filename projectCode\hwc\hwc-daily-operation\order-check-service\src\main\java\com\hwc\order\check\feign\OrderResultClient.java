package com.hwc.order.check.feign;

import com.hwc.common.dto.OrderCheckResultDTO;
import com.hwc.common.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单结果存储服务客户端
 *
 * <AUTHOR>
 */
@FeignClient(name = "order-result-service", path = "/api/order/result")
public interface OrderResultClient {

    /**
     * 保存订单检查结果
     *
     * @param result 检查结果
     * @return 保存结果
     */
    @PostMapping("/save")
    Result<Void> saveCheckResult(@RequestBody OrderCheckResultDTO result);
}
